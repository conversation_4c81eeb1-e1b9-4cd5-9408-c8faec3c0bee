#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحلیل دقیق ساختار masael برای درک الگوی صحیح
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_masael_urls():
    """تحلیل URL های مختلف masael"""
    
    masael_urls = [
        ("Vacib namazlar", "https://www.leader.ir/az/book/246/1?sn=31659"),
        ("Gündəlik vacib namazlar", "https://www.leader.ir/az/book/246/1?sn=31660"),
        ("Sübh namazının vaxtı", "https://www.leader.ir/az/book/246/1?sn=32911"),
        ("Zöhr və əsr namazlarının vaxtı", "https://www.leader.ir/az/book/246/1?sn=31661"),
    ]
    
    for title, url in masael_urls:
        print(f"\n{'='*60}")
        print(f"🔍 تحلیل: {title}")
        print(f"🌐 URL: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # جستجوی عناصر strong
            strong_elements = soup.find_all('strong')
            masael_strongs = []
            
            for strong in strong_elements:
                text = strong.get_text().strip()
                if re.match(r'Məsələ\s+\d+\.', text, re.IGNORECASE):
                    masael_strongs.append(text)
            
            print(f"📊 تعداد مسائل یافت شده: {len(masael_strongs)}")
            
            # نمایش 5 مسئله اول
            print(f"📝 5 مسئله اول:")
            for i, masael in enumerate(masael_strongs[:5], 1):
                print(f"  {i}. {masael}")
            
            # بررسی محتوای صفحه برای الگوی خاص
            page_text = soup.get_text()
            
            # جستجوی عنوان masael در متن صفحه
            title_in_page = title.replace(" ", r"\s+")
            title_pattern = re.compile(title_in_page, re.IGNORECASE)
            title_matches = title_pattern.findall(page_text)
            
            print(f"🔍 عنوان '{title}' در صفحه: {len(title_matches)} بار یافت شد")
            
            # جستجوی بخش‌های مختلف صفحه
            # بررسی navigation یا breadcrumb
            nav_elements = soup.find_all(['nav', 'div'], class_=re.compile(r'nav|breadcrumb|menu', re.I))
            print(f"📍 عناصر navigation: {len(nav_elements)}")
            
            # بررسی لینک‌های موجود
            links = soup.find_all('a', href=True)
            masael_links = []
            for link in links:
                href = link.get('href', '')
                if 'sn=' in href:
                    link_text = link.get_text().strip()
                    if link_text and len(link_text) > 5:
                        masael_links.append((link_text, href))
            
            print(f"🔗 لینک‌های masael: {len(masael_links)}")
            
            # نمایش چند لینک اول
            for i, (link_text, href) in enumerate(masael_links[:3], 1):
                print(f"  {i}. {link_text} -> {href}")
            
        except Exception as e:
            print(f"❌ خطا: {e}")


def analyze_specific_masael_content():
    """تحلیل محتوای خاص یک masael"""
    print(f"\n{'='*60}")
    print("🔍 تحلیل محتوای خاص masael")
    
    # تست با URL که احتمالاً محتوای خاص دارد
    url = "https://www.leader.ir/az/book/246/1?sn=31659"  # Vacib namazlar
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        page_text = soup.get_text()
        
        # جستجوی الگوی مسائل
        masael_pattern = re.compile(
            r'(Məsələ\s+\d+\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)', 
            re.DOTALL | re.IGNORECASE
        )
        
        matches = masael_pattern.findall(page_text)
        
        print(f"📊 تعداد مسائل با regex: {len(matches)}")
        
        # تحلیل 3 مسئله اول
        for i, (header, text) in enumerate(matches[:3], 1):
            clean_text = text.strip()
            clean_text = re.sub(r'\s+', ' ', clean_text)
            
            print(f"\n📝 مسئله {i}:")
            print(f"  Header: {header}")
            print(f"  Text: {clean_text[:100]}...")
            
            # بررسی اینکه آیا این مسئله مربوط به "Vacib namazlar" است
            if i == 1:  # مسئله اول
                if "Vacib namazlar aşağıdakılardır" in clean_text:
                    print("  ✅ این مسئله مربوط به 'Vacib namazlar' است")
                else:
                    print("  ❌ این مسئله مربوط به 'Vacib namazlar' نیست")
            
            elif i == 2:  # مسئله دوم
                if "Gündəlik vacib namazlar İslam dininin" in clean_text:
                    print("  ✅ این مسئله مربوط به 'Gündəlik vacib namazlar' است")
                else:
                    print("  ❌ این مسئله مربوط به 'Gündəlik vacib namazlar' نیست")
        
        # بررسی ساختار HTML برای یافتن الگوی تقسیم‌بندی
        print(f"\n🔍 بررسی ساختار HTML:")
        
        # جستجوی div ها یا section هایی که ممکن است محتوا را تقسیم کنند
        content_divs = soup.find_all('div', class_=True)
        print(f"📦 تعداد div های دارای class: {len(content_divs)}")
        
        # جستجوی h1, h2, h3 برای عناوین
        headers = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        print(f"📑 تعداد headers: {len(headers)}")
        
        for i, header in enumerate(headers[:5], 1):
            print(f"  {i}. {header.name}: {header.get_text().strip()[:50]}...")
        
    except Exception as e:
        print(f"❌ خطا: {e}")


if __name__ == "__main__":
    analyze_masael_urls()
    analyze_specific_masael_content()
