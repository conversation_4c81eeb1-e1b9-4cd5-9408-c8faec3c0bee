#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست الگوریتم جدید استخراج محتوای masael
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def test_new_extraction():
    """تست الگوریتم جدید"""
    print("🚀 تست الگوریتم جدید استخراج محتوای masael")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # تست با "Vacib namazlar" که 161 مسئله دارد
    test_item = {
        'id': '31659',
        'title': 'Vacib namazlar',
        'type': 'masael',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31659'
    }
    
    print(f"📖 تست با: {test_item['title']}")
    print(f"🌐 URL: {test_item['url']}")
    
    # استخراج محتوا
    crawler.extract_masael_content(test_item)
    
    # بررسی نتایج
    if test_item.get('masael_contents'):
        contents = test_item['masael_contents']
        print(f"\n✅ نتایج:")
        print(f"📊 تعداد مسائل استخراج شده: {len(contents)}")
        
        # نمایش 5 مسئله اول
        print(f"\n📝 5 مسئله اول:")
        for i, masael in enumerate(contents[:5], 1):
            print(f"\n{i}. {masael['header']}")
            print(f"   متن: {masael['text'][:100]}...")
        
        # بررسی مسئله اول که باید درست باشد
        if len(contents) > 0:
            first_masael = contents[0]
            print(f"\n🔍 بررسی مسئله اول:")
            print(f"Header: {first_masael['header']}")
            print(f"Text: {first_masael['text']}")
            
            # بررسی صحت
            expected_text_start = "Vacib namazlar aşağıdakılardır"
            if first_masael['text'].startswith(expected_text_start):
                print("✅ مسئله اول صحیح است!")
            else:
                print("❌ مسئله اول صحیح نیست!")
                print(f"انتظار: {expected_text_start}")
                print(f"دریافت: {first_masael['text'][:50]}...")
        
        # تست با یک مسئله دیگر
        print(f"\n" + "="*30)
        print("🔍 تست با مسئله دیگر...")
        
        test_item2 = {
            'id': '31664',
            'title': 'Namazların ardıcıllığı',
            'type': 'masael',
            'url': 'https://www.leader.ir/az/book/246/1?sn=31664'
        }
        
        crawler.extract_masael_content(test_item2)
        
        if test_item2.get('masael_contents'):
            contents2 = test_item2['masael_contents']
            print(f"📊 تعداد مسائل: {len(contents2)}")
            
            if len(contents2) > 0:
                print(f"نمونه: {contents2[0]['header']} - {contents2[0]['text'][:80]}...")
        
    else:
        print("❌ هیچ محتوایی استخراج نشد!")


if __name__ == "__main__":
    test_new_extraction()
