#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست محدود کراولر - فقط یک sub_category
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def main():
    """تست محدود"""
    print("🚀 تست محدود کراولر")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # ایجاد یک sub_category تست
    test_item = {
        'id': '31669',
        'title': '<PERSON>az paltarının şərtləri',
        'type': 'sub_category',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31669',
        'children': []
    }
    
    # تست استخراج زیرمجموعه‌ها
    crawler.extract_deep_subcategories(test_item)
    
    # نمایش نتایج
    print("\n📋 نتایج:")
    print(f"📁 {test_item['title']} ({len(test_item['children'])} زیرمجموعه):")
    for child in test_item['children']:
        print(f"  - {child['type']}: {child['title']} (ID: {child['id']})")
    
    print("\n✅ تست تکمیل شد!")


if __name__ == "__main__":
    main()
