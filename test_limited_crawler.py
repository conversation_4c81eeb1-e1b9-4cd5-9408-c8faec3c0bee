#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست کراولر محدود - حداکثر 50 آیتم برای هر دسته
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def main():
    """تست کراولر محدود"""
    print("🚀 تست کراولر محدود")
    print("📝 حداکثر 50 masael برای هر دسته‌بندی اصلی")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # مرحله 1: استخراج دسته‌بندی‌های اصلی
    main_categories = crawler.extract_main_categories()
    
    if not main_categories:
        print("❌ هیچ دسته‌بندی اصلی یافت نشد!")
        return
    
    print(f"✅ {len(main_categories)} دسته‌بندی اصلی یافت شد")
    
    # مرحله 2: استخراج زیرمجموعه‌ها (محدود)
    print("\n🔍 مرحله 2: استخراج زیرمجموعه‌ها...")
    for category in main_categories:
        crawler.extract_subcategories(category)
        print(f"✅ {category['title']}: {len(category['children'])} زیرمجموعه")
    
    # مرحله 3: استخراج عمیق (محدود به 3 sub_category)
    print("\n🔍 مرحله 3: استخراج عمیق (محدود)...")
    for category in main_categories:
        sub_count = 0
        for item in category['children']:
            if item['type'] == 'sub_category' and sub_count < 3:
                crawler.extract_deep_subcategories(item)
                sub_count += 1
    
    # مرحله 4: استخراج محتوا (محدود به 10 masael)
    print("\n🔍 مرحله 4: استخراج محتوا (محدود)...")
    
    def extract_limited_content(items, max_items=10):
        processed = 0
        for item in items:
            if processed >= max_items:
                break
            if item['type'] == 'masael':
                crawler.extract_masael_content(item)
                processed += 1
                print(f"  📖 {processed}/{max_items}: {item['title']}")
            elif item['type'] == 'sub_category' and item.get('children'):
                sub_processed = extract_limited_content(item['children'], max_items - processed)
                processed += sub_processed
        return processed
    
    total_processed = 0
    for category in main_categories:
        print(f"\n📁 {category['title']}:")
        category_processed = extract_limited_content(category['children'], 10)
        total_processed += category_processed
    
    print(f"\n📊 مجموع: {total_processed} masael پردازش شد")
    
    # تولید خروجی‌های محدود
    print("\n🔍 تولید خروجی‌ها...")
    crawler.save_to_json(main_categories, "leader_crawl_test.json")
    crawler.generate_html_preview(main_categories, "leader_crawl_test.html")
    
    # آمار نهایی
    print("\n📊 آمار نهایی:")
    print(f"  - صفحات cache: {len(crawler.page_cache)}")
    print(f"  - آیتم‌های پردازش شده: {len(crawler.processed_items)}")
    print(f"  - masael های پردازش شده: {total_processed}")
    
    print("\n✅ تست محدود تکمیل شد!")


if __name__ == "__main__":
    main()
