#!/usr/bin/env python3
"""
ساختار یاب بهبود یافته سایت leader.ir با استفاده از Playwright
این اسکریپت ساختار درختی دسته‌بندی‌های سایت را استخراج می‌کند
"""

import asyncio
import json
from playwright.async_api import async_playwright

class ImprovedLeaderAnalyzer:
    def __init__(self, max_items=None):
        self.url = "https://www.leader.ir/az/book/246/NAMAZ-V%C6%8F-ORUC-R%C4%B0SAL%C6%8FS%C4%B0"
        self.structure = {}
        self.browser = None
        self.page = None
        self.max_items = max_items  # محدودیت تعداد آیتم‌ها
        self.processed_items = 0    # شمارنده آیتم‌های پردازش شده
        self.extraction_cache = {}  # Cache برای نتایج extract_children
        self.processed_objects = set()  # Global tracking برای آبجکت‌های پردازش شده
        self.parent_child_relationships = {}  # ردیابی روابط والد-فرزند برای جلوگیری از تکرار
    
    async def init_browser(self):
        """مرورگر را راه‌اندازی می‌کند"""
        playwright = await async_playwright().start()
        self.browser = await playwright.chromium.launch(
            headless=True,   # حالت headless برای سرعت بیشتر
            executable_path="/usr/bin/google-chrome"
        )
        self.page = await self.browser.new_page()
        await self.page.set_viewport_size({"width": 1920, "height": 1080})
        
    async def navigate_to_site(self):
        """به سایت هدف می‌رود"""
        print(f"در حال بارگذاری سایت: {self.url}")
        await self.page.goto(self.url, wait_until="networkidle")
        await asyncio.sleep(3)
        
    async def find_and_click_categories(self):
        """دسته‌بندی‌های اصلی را پیدا کرده و روی آن‌ها کلیک می‌کند"""
        print("در حال جستجو برای دسته‌بندی‌های اصلی...")
        
        # جستجو برای عناصر NAMAZ و ORUC با کلاس tree_expand
        categories_found = await self.page.evaluate("""
            () => {
                const categories = [];
                const elements = document.querySelectorAll('h5.tree_expand');
                
                for (let element of elements) {
                    const text = element.textContent.trim();
                    if (text === 'NAMAZ' || text === 'ORUC') {
                        categories.push({
                            text: text,
                            id: element.parentElement ? element.parentElement.id : '',
                            className: element.className,
                            visible: element.offsetParent !== null
                        });
                    }
                }
                
                return categories;
            }
        """)
        
        print(f"دسته‌بندی‌های یافت شده: {len(categories_found)}")
        for cat in categories_found:
            print(f"  - {cat['text']} (ID: {cat['id']}, Visible: {cat['visible']})")
        
        # کلیک روی هر دسته‌بندی
        for category in categories_found:
            await self.click_and_explore_category(category)
            
        return categories_found
    
    async def click_and_explore_category(self, category):
        """روی یک دسته‌بندی کلیک کرده و محتوای آن را کاوش می‌کند"""
        print(f"\n=== کاوش دسته‌بندی: {category['text']} ===")
        
        try:
            # اگر عنصر visible نیست، scroll کن
            if not category['visible']:
                await self.scroll_to_element(category)
            
            # کلیک روی دسته‌بندی با استفاده از JavaScript
            await self.page.evaluate(f"""
                () => {{
                    const h5Elements = document.querySelectorAll('h5.tree_expand');
                    for (let h5 of h5Elements) {{
                        if (h5.textContent && h5.textContent.trim() === '{category['text']}') {{
                            h5.click();
                            return true;
                        }}
                    }}
                    return false;
                }}
            """)
            print(f"کلیک روی {category['text']} انجام شد")
            
            # صبر برای بارگذاری محتوا
            await asyncio.sleep(2)
            
            # استخراج زیردسته‌ها
            subcategories = await self.extract_subcategories_for_category(category)

            # طبقه‌بندی زیردسته‌ها
            print(f"در حال طبقه‌بندی {len(subcategories)} زیردسته...")
            classified_subcategories = await self.classify_subcategories(subcategories)

            # حفظ ترتیب اصلی سایت - بدون تغییر ترتیب
            all_items = classified_subcategories

            # تفکیک برای آمار
            masael_items = [sub for sub in all_items if sub.get('element_type') == 'masael']
            category_items = [sub for sub in all_items if sub.get('element_type') == 'category']
            other_items = [sub for sub in all_items if sub.get('element_type') not in ['masael', 'category']]

            # شمارش کل فرزندان
            total_children = sum(len(sub.get('children', [])) for sub in category_items)

            # ذخیره در ساختار - همه موارد در یک لیست
            self.structure[category['text']] = {
                'category_info': category,
                'subcategories': all_items,  # همه موارد
                'total_masael': len(masael_items),
                'total_categories': len(category_items),
                'total_children': total_children,
                'total_other': len(other_items)
            }

            print(f"نتایج طبقه‌بندی:")
            print(f"  - Masael: {len(masael_items)}")
            print(f"  - دسته‌بندی‌ها: {len(category_items)}")
            print(f"  - کل فرزندان: {total_children}")
            print(f"  - سایر موارد: {len(other_items)}")

            # نمایش نمونه‌ها
            if masael_items:
                print(f"  نمونه Masael:")
                for i, item in enumerate(masael_items[:3]):
                    print(f"    {i+1}. {item['text'][:40]}...")

            if category_items:
                print(f"  نمونه دسته‌بندی‌ها:")
                for i, item in enumerate(category_items[:3]):
                    children_count = len(item.get('children', []))
                    masael_status = "دارای masael" if item.get('has_masael_content') else "بدون masael"
                    print(f"    {i+1}. {item['text'][:40]}... ({masael_status}, {children_count} فرزند)")
                
        except Exception as e:
            print(f"خطا در کاوش {category['text']}: {e}")
    
    async def scroll_to_element(self, category):
        """به عنصر مورد نظر scroll می‌کند"""
        try:
            await self.page.evaluate(f"""
                () => {{
                    const element = document.querySelector('h5:has-text("{category['text']}")');
                    if (element) {{
                        element.scrollIntoView({{behavior: 'smooth', block: 'center'}});
                    }}
                }}
            """)
            await asyncio.sleep(1)
        except Exception as e:
            print(f"خطا در scroll: {e}")
    
    async def extract_subcategories_for_category(self, category):
        """فقط فرزندان مستقیم سطح اول را استخراج می‌کند"""
        subcategories = await self.page.evaluate(f"""
            (categoryText) => {{
                const subs = [];
                const processedTexts = new Set();

                // پیدا کردن container دسته‌بندی
                const h5Elements = document.querySelectorAll('h5');
                let categoryElement = null;

                for (let h5 of h5Elements) {{
                    if (h5.textContent && h5.textContent.trim() === categoryText) {{
                        categoryElement = h5;
                        break;
                    }}
                }}

                if (!categoryElement) return subs;

                const container = categoryElement.parentElement;
                if (!container) return subs;

                // فقط فرزندان مستقیم سطح اول
                const directChildren = container.querySelectorAll(':scope > * h5.tree_expand, :scope h5.tree_expand');

                for (let element of directChildren) {{
                    const text = element.textContent ? element.textContent.trim() : '';

                    // فیلتر کردن عناصر نامناسب
                    const isInvalidElement = text.includes('Çap versiyası') ||
                                           text.includes('Leader.ir') ||
                                           text.includes('NAMAZ VƏ ORUC RİSALƏSİ') ||
                                           element.classList.contains('pull-left');

                    // فقط فرزندان مستقیم که والد آن‌ها همین دسته‌بندی است
                    if (text.length > 3 && text.length < 100 &&
                        text !== categoryText &&
                        !isInvalidElement &&
                        !processedTexts.has(text)) {{

                        // بررسی اینکه آیا این عنصر واقعاً فرزند مستقیم است
                        let isDirectChild = false;
                        let parent = element.parentElement;
                        let depth = 0;

                        while (parent && depth < 5) {{
                            if (parent === container) {{
                                isDirectChild = true;
                                break;
                            }}
                            parent = parent.parentElement;
                            depth++;
                        }}

                        if (isDirectChild) {{
                            subs.push({{
                                text: text,
                                tagName: element.tagName,
                                className: element.className,
                                id: element.id,
                                hasChildren: element.classList.contains('tree_expand'),
                                depth: depth
                            }});
                            processedTexts.add(text);
                        }}
                    }}
                }}

                return subs;
            }}
        """, category['text'])

        return subcategories

    async def analyze_element_type(self, element_text):
        """تشخیص نوع عنصر: masael یا دسته‌بندی"""
        # کلیک روی عنصر و بررسی محتوای باز شده
        element_info = await self.page.evaluate(f"""
            (elementText) => {{
                // تابع استخراج محتوای پاک masael با استفاده از ساختار HTML
                function extractCleanMasaelContent(container) {{
                    if (!container) return '';

                    // استفاده از روش محافظه‌کارانه: استخراج کل محتوا و فقط حذف footnotes
                    const clonedContainer = container.cloneNode(true);

                    // حذف تمام عناصر small (footnotes)
                    const smallElements = clonedContainer.querySelectorAll('small');
                    smallElements.forEach(small => small.remove());

                    // حذف footnote links
                    const footnoteLinks = clonedContainer.querySelectorAll('a[href*="#_ftn"]');
                    footnoteLinks.forEach(link => link.remove());

                    // حذف عناصر sup که معمولاً footnote reference هستند
                    const supElements = clonedContainer.querySelectorAll('sup');
                    supElements.forEach(sup => sup.remove());

                    // بجای حذف همه چیز بعد از hr، فقط محتوای footnote را حذف کن
                    // hr معمولاً footnote content را از main content جدا می‌کند
                    // اما ممکن است masael content هم بعد از hr باشد

                    return clonedContainer.textContent.trim();
                }}

                // تابع استخراج متن از عنصر matn با حذف footnotes
                function extractTextFromMatnElement(matnElement) {{
                    // کلون کردن عنصر برای عدم تغییر اصل
                    const clonedElement = matnElement.cloneNode(true);

                    // حذف تمام عناصر small (footnotes)
                    const smallElements = clonedElement.querySelectorAll('small');
                    smallElements.forEach(small => small.remove());

                    // حذف تمام لینک‌های footnote
                    const footnoteLinks = clonedElement.querySelectorAll('a[href*="#_ftn"]');
                    footnoteLinks.forEach(link => link.remove());

                    // حذف عناصر sup که معمولاً footnote reference هستند
                    const supElements = clonedElement.querySelectorAll('sup');
                    supElements.forEach(sup => sup.remove());

                    // حذف محتوای بعد از hr اگر وجود دارد
                    const hrElement = clonedElement.querySelector('hr');
                    if (hrElement) {{
                        let nextSibling = hrElement.nextSibling;
                        while (nextSibling) {{
                            const toRemove = nextSibling;
                            nextSibling = nextSibling.nextSibling;
                            if (toRemove.nodeType === Node.ELEMENT_NODE || toRemove.nodeType === Node.TEXT_NODE) {{
                                toRemove.remove();
                            }}
                        }}
                        hrElement.remove();
                    }}

                    return clonedElement.textContent.trim();
                }}

                // تابع استخراج متن با فیلتر HTML
                function extractTextWithHtmlFiltering(container) {{
                    const clonedContainer = container.cloneNode(true);

                    // حذف hr و همه چیز بعد از آن
                    const hrElement = clonedContainer.querySelector('hr');
                    if (hrElement) {{
                        let nextSibling = hrElement.nextSibling;
                        while (nextSibling) {{
                            const toRemove = nextSibling;
                            nextSibling = nextSibling.nextSibling;
                            if (toRemove.nodeType === Node.ELEMENT_NODE || toRemove.nodeType === Node.TEXT_NODE) {{
                                toRemove.remove();
                            }}
                        }}
                        hrElement.remove();
                    }}

                    // حذف تمام عناصر small
                    const smallElements = clonedContainer.querySelectorAll('small');
                    smallElements.forEach(small => small.remove());

                    // حذف footnote links
                    const footnoteLinks = clonedContainer.querySelectorAll('a[href*="#_ftn"]');
                    footnoteLinks.forEach(link => link.remove());

                    // حذف عناصر sup
                    const supElements = clonedContainer.querySelectorAll('sup');
                    supElements.forEach(sup => sup.remove());

                    return clonedContainer.textContent || '';
                }}

                // پیدا کردن عنصر
                const h5Elements = document.querySelectorAll('h5.tree_expand');
                let targetElement = null;

                for (let h5 of h5Elements) {{
                    if (h5.textContent && h5.textContent.trim() === elementText) {{
                        targetElement = h5;
                        break;
                    }}
                }}

                if (!targetElement) return null;

                // کلیک روی عنصر
                targetElement.click();

                // صبر کوتاه برای بارگذاری
                return new Promise(resolve => {{
                    setTimeout(() => {{
                        // بررسی محتوای باز شده
                        const container = targetElement.parentElement;
                        if (!container) {{
                            resolve({{ type: 'unknown', hasContent: false }});
                            return;
                        }}

                        // استخراج محتوای masael با استفاده از ساختار HTML
                        const allText = extractCleanMasaelContent(container);

                        // بررسی دقیق‌تر برای محتوای مربوطه
                        // جستجو برای بخش‌هایی که شامل نام عنصر و Məsələ هستند
                        const textLines = allText.split('\\n').map(line => line.trim()).filter(line => line.length > 0);

                        let relevantContent = '';
                        let foundElementName = false;

                        for (let i = 0; i < textLines.length; i++) {{
                            const line = textLines[i];

                            // اگر خط شامل نام عنصر است
                            if (line.includes(elementText)) {{
                                foundElementName = true;
                                relevantContent += line + '\\n';

                                // تمام خطوط بعدی را اضافه کن تا به عنصر دیگری برسیم
                                for (let j = i + 1; j < textLines.length; j++) {{
                                    const currentLine = textLines[j];

                                    // توقف اگر به عنصر دیگری رسیدیم (نه فقط Çap versiyası)
                                    if (currentLine.includes('Çap versiyası') && j > i + 5) {{
                                        break;
                                    }}

                                    // توقف اگر به دسته‌بندی جدید رسیدیم
                                    if (currentLine.match(/^[A-Z][a-z].*/) && currentLine.length < 50 && j > i + 5) {{
                                        break;
                                    }}

                                    relevantContent += currentLine + '\\n';
                                }}
                                break;
                            }}
                        }}

                        // اگر محتوای مربوطه پیدا نشد، از کل متن استفاده کن
                        if (!foundElementName) {{
                            relevantContent = allText;
                        }}

                        // برای عناصر خاص که مشکل truncation دارند، از کل متن استفاده کن
                        if (elementText === 'Namazda örtünmə' || relevantContent.length < allText.length * 0.8) {{
                            relevantContent = allText;
                        }}

                        // بررسی وجود کلمه کلیدی Məsələ در محتوای مربوطه
                        const hasMasaelKeyword = relevantContent.includes('Məsələ') ||
                                               relevantContent.includes('məsələ');

                        // محتوای masael فقط اگر در محتوای مربوطه باشد
                        const hasMasaelContent = foundElementName && hasMasaelKeyword;

                        // جستجو برای زیردسته‌های بیشتر
                        const subElements = container.querySelectorAll('h5.tree_expand');
                        const hasSubcategories = subElements.length > 0;

                        let elementType = 'unknown';
                        if (hasMasaelContent && hasSubcategories) {{
                            // دارای محتوای masael و زیردسته - احتمالاً masael با زیرشاخه
                            elementType = 'masael';
                        }} else if (hasMasaelContent && !hasSubcategories) {{
                            // فقط محتوای masael - قطعاً masael
                            elementType = 'masael';
                        }} else if (!hasMasaelContent && hasSubcategories) {{
                            // فقط زیردسته بدون محتوای masael - دسته‌بندی
                            elementType = 'category';
                        }}

                        resolve({{
                            type: elementType,
                            hasContent: hasMasaelContent,
                            hasSubcategories: hasSubcategories,
                            contentPreview: relevantContent.substring(0, 2000)  // افزایش محدودیت به 2000 کاراکتر
                        }});
                    }}, 1000);
                }});
            }}
        """, element_text)

        return element_info

    def is_duplicate_child(self, parent_text, child_text):
        """بررسی می‌کند که آیا فرزند قبلاً برای این والد اضافه شده است"""
        parent_key = parent_text.strip()
        if parent_key not in self.parent_child_relationships:
            self.parent_child_relationships[parent_key] = set()

        child_key = child_text.strip()
        if child_key in self.parent_child_relationships[parent_key]:
            return True

        self.parent_child_relationships[parent_key].add(child_key)
        return False

    def validate_hierarchical_structure(self, children_list, parent_text):
        """اعتبارسنجی ساختار سلسله‌مراتبی و حذف تکرارها"""
        if not children_list:
            return children_list

        validated_children = []
        seen_texts = set()

        for child in children_list:
            child_text = child.get('text', '').strip()

            # حذف تکرارها در همین سطح
            if child_text in seen_texts:
                print(f"    ⚠️  حذف فرزند تکراری در سطح: {child_text[:30]}...")
                continue

            # بررسی اینکه فرزند نباید همان والد باشد - مگر اینکه self-referencing masael باشد
            if child_text == parent_text.strip():
                # اگر فرزند self-referencing masael است و محتوای masael دارد، آن را نگه دار
                if (child.get('element_type') == 'masael' and
                    child.get('is_self') == True and
                    child.get('has_masael_content') == True):
                    print(f"    ✅  نگه‌داری self-referencing masael: {child_text[:30]}...")
                else:
                    print(f"    ⚠️  حذف فرزند خودی: {child_text[:30]}...")
                    continue

            seen_texts.add(child_text)
            validated_children.append(child)

        return validated_children

    async def classify_subcategories(self, subcategories):
        """زیردسته‌ها را طبقه‌بندی می‌کند"""
        classified = []

        for sub in subcategories:
            # بررسی محدودیت تعداد
            if self.max_items and self.processed_items >= self.max_items:
                print(f"\n🛑 محدودیت تعداد رسید: {self.max_items} آیتم پردازش شد")
                print(f"متوقف کردن پردازش...")
                break

            # بررسی تکرار آبجکت
            if sub['text'] in self.processed_objects:
                print(f"  ⚠️  آبجکت تکراری رد شد: {sub['text'][:50]}...")
                continue

            self.processed_objects.add(sub['text'])
            self.processed_items += 1
            print(f"\n📊 پردازش آیتم {self.processed_items}/{self.max_items or '∞'}: {sub['text'][:50]}...")
            # بررسی الگوی عناصر: H5 + tree_expand + hasChildren
            if (sub['tagName'] == 'H5' and
                'tree_expand' in sub['className'] and
                sub['hasChildren']):

                # بررسی محتوا برای تشخیص نوع
                element_info = await self.analyze_element_type(sub['text'])
                if element_info:
                    sub['has_masael_content'] = element_info['hasContent']
                    sub['content_preview'] = element_info.get('contentPreview', '')

                    # استخراج فرزندان برای تشخیص نوع
                    children = await self.extract_children(sub['text'])

                    # تشخیص نوع بر اساس محتوای واقعی فرزندان، نه فقط hasChildren flag
                    sub['children'] = children

                    # بررسی تعداد فرزندان واقعی (غیر از خودش)
                    real_children = [c for c in children if c.get('text') != sub['text'] or not c.get('is_self', False)]

                    if len(real_children) > 0:
                        # دارای فرزندان واقعی - دسته‌بندی
                        sub['element_type'] = 'category'
                        print(f"  تحلیل {sub['text']}: نوع = دسته‌بندی (دارای {len(real_children)} فرزند واقعی)")
                    elif element_info['hasContent']:
                        # بدون فرزندان واقعی اما دارای محتوای masael - masael
                        sub['element_type'] = 'masael'
                        sub['children'] = []  # masael نباید فرزند داشته باشد
                        print(f"  تحلیل {sub['text']}: نوع = masael (بدون فرزندان واقعی، دارای محتوای masael)")
                    else:
                        # بدون فرزندان و بدون محتوای masael
                        sub['element_type'] = 'other'
                        sub['children'] = []
                        print(f"  تحلیل {sub['text']}: نوع = other (بدون فرزندان و محتوای masael)")
                else:
                    sub['has_masael_content'] = False
                    sub['content_preview'] = ''

                    # استخراج فرزندان برای عناصری که analyze_element_type ناموفق بود
                    children = await self.extract_children(sub['text'])
                    sub['children'] = children

                    # بررسی تعداد فرزندان واقعی برای تشخیص نوع
                    real_children = [c for c in children if c.get('text') != sub['text'] or not c.get('is_self', False)]

                    if len(real_children) > 0:
                        sub['element_type'] = 'category'
                        print(f"  تحلیل {sub['text']}: نوع = دسته‌بندی (پیش‌فرض، دارای {len(real_children)} فرزند)")
                    else:
                        sub['element_type'] = 'other'
                        sub['children'] = []
                        print(f"  تحلیل {sub['text']}: نوع = other (پیش‌فرض، بدون فرزندان)")

            # بررسی اضافی: اگر عنصر hasChildren دارد اما هنوز children استخراج نشده
            elif sub.get('hasChildren') and not sub.get('children'):
                print(f"  🔧 عنصر {sub['text'][:30]}... دارای hasChildren اما بدون children - در حال استخراج...")

                # تلاش برای استخراج فرزندان
                children = await self.extract_children(sub['text'])
                sub['children'] = children
                sub['has_masael_content'] = False
                sub['content_preview'] = ''

                # بررسی تعداد فرزندان واقعی برای تشخیص نوع
                real_children = [c for c in children if c.get('text') != sub['text'] or not c.get('is_self', False)]

                if len(real_children) > 0:
                    sub['element_type'] = 'category'
                    print(f"  تحلیل {sub['text']}: نوع = دسته‌بندی (اجباری، دارای {len(real_children)} فرزند)")
                else:
                    sub['element_type'] = 'other'
                    sub['children'] = []
                    print(f"  تحلیل {sub['text']}: نوع = other (اجباری، بدون فرزندان)")

            else:
                sub['element_type'] = 'simple_item'
                sub['has_masael_content'] = False
                sub['content_preview'] = ''
                sub['children'] = []
                print(f"  تحلیل {sub['text']}: نوع = آیتم ساده")

            # اطمینان از وجود کلیدهای ضروری
            if 'children' not in sub:
                sub['children'] = []
            if 'has_masael_content' not in sub:
                sub['has_masael_content'] = False
            if 'content_preview' not in sub:
                sub['content_preview'] = ''
            if 'element_type' not in sub:
                sub['element_type'] = 'unknown'

            classified.append(sub)

        return classified

    async def extract_children(self, parent_text):
        """فرزندان یک دسته‌بندی را استخراج می‌کند و نوع هر فرزند را تشخیص می‌دهد"""
        # استفاده از cache برای جلوگیری از تکرار
        if parent_text in self.extraction_cache:
            print(f"  📋 استفاده از cache برای: {parent_text}")
            return self.extraction_cache[parent_text]

        children = []

        try:
            # کلیک دوباره روی عنصر برای اطمینان از باز بودن
            await self.page.evaluate(f"""
                () => {{
                    const h5Elements = document.querySelectorAll('h5.tree_expand');
                    for (let h5 of h5Elements) {{
                        if (h5.textContent && h5.textContent.trim() === '{parent_text}') {{
                            h5.click();
                            return true;
                        }}
                    }}
                    return false;
                }}
            """)

            await asyncio.sleep(1)  # صبر برای بارگذاری

            # استخراج فرزندان
            children_info = await self.page.evaluate(f"""
                (parentText) => {{
                    // تابع استخراج محتوای پاک masael با استفاده از ساختار HTML
                    function extractCleanMasaelContent(container) {{
                        if (!container) return '';

                        // استفاده از روش محافظه‌کارانه: استخراج کل محتوا و فقط حذف footnotes
                        const clonedContainer = container.cloneNode(true);

                        // حذف تمام عناصر small (footnotes)
                        const smallElements = clonedContainer.querySelectorAll('small');
                        smallElements.forEach(small => small.remove());

                        // حذف footnote links
                        const footnoteLinks = clonedContainer.querySelectorAll('a[href*="#_ftn"]');
                        footnoteLinks.forEach(link => link.remove());

                        // حذف عناصر sup که معمولاً footnote reference هستند
                        const supElements = clonedContainer.querySelectorAll('sup');
                        supElements.forEach(sup => sup.remove());

                        // بجای حذف همه چیز بعد از hr، فقط محتوای footnote را حذف کن
                        // hr معمولاً footnote content را از main content جدا می‌کند
                        // اما ممکن است masael content هم بعد از hr باشد

                        return clonedContainer.textContent.trim();
                    }}

                    // تابع استخراج متن از عنصر matn با حذف footnotes
                    function extractTextFromMatnElement(matnElement) {{
                        const clonedElement = matnElement.cloneNode(true);

                        // حذف تمام عناصر small (footnotes)
                        const smallElements = clonedElement.querySelectorAll('small');
                        smallElements.forEach(small => small.remove());

                        // حذف تمام لینک‌های footnote
                        const footnoteLinks = clonedElement.querySelectorAll('a[href*="#_ftn"]');
                        footnoteLinks.forEach(link => link.remove());

                        // حذف عناصر sup که معمولاً footnote reference هستند
                        const supElements = clonedElement.querySelectorAll('sup');
                        supElements.forEach(sup => sup.remove());

                        // حذف محتوای بعد از hr اگر وجود دارد
                        const hrElement = clonedElement.querySelector('hr');
                        if (hrElement) {{
                            let nextSibling = hrElement.nextSibling;
                            while (nextSibling) {{
                                const toRemove = nextSibling;
                                nextSibling = nextSibling.nextSibling;
                                if (toRemove.nodeType === Node.ELEMENT_NODE || toRemove.nodeType === Node.TEXT_NODE) {{
                                    toRemove.remove();
                                }}
                            }}
                            hrElement.remove();
                        }}

                        return clonedElement.textContent.trim();
                    }}

                    // تابع استخراج متن با فیلتر HTML
                    function extractTextWithHtmlFiltering(container) {{
                        const clonedContainer = container.cloneNode(true);

                        // حذف hr و همه چیز بعد از آن
                        const hrElement = clonedContainer.querySelector('hr');
                        if (hrElement) {{
                            let nextSibling = hrElement.nextSibling;
                            while (nextSibling) {{
                                const toRemove = nextSibling;
                                nextSibling = nextSibling.nextSibling;
                                if (toRemove.nodeType === Node.ELEMENT_NODE || toRemove.nodeType === Node.TEXT_NODE) {{
                                    toRemove.remove();
                                }}
                            }}
                            hrElement.remove();
                        }}

                        // حذف تمام عناصر small
                        const smallElements = clonedContainer.querySelectorAll('small');
                        smallElements.forEach(small => small.remove());

                        // حذف footnote links
                        const footnoteLinks = clonedContainer.querySelectorAll('a[href*="#_ftn"]');
                        footnoteLinks.forEach(link => link.remove());

                        // حذف عناصر sup
                        const supElements = clonedContainer.querySelectorAll('sup');
                        supElements.forEach(sup => sup.remove());

                        return clonedContainer.textContent || '';
                    }}

                    const children = [];
                    const debug = [];

                    // پیدا کردن عنصر والد
                    const h5Elements = document.querySelectorAll('h5');
                    let parentElement = null;

                    for (let h5 of h5Elements) {{
                        if (h5.textContent && h5.textContent.trim() === parentText) {{
                            parentElement = h5;
                            break;
                        }}
                    }}

                    if (!parentElement) {{
                        debug.push('Parent element not found');
                        return {{ children, debug }};
                    }}

                    const container = parentElement.parentElement;
                    if (!container) {{
                        debug.push('Container not found');
                        return {{ children, debug }};
                    }}

                    debug.push('Container found: ' + container.tagName);

                    // اگر دارای محتوای masael است، خودش را به عنوان اولین فرزند اضافه کن
                    // استفاده از تابع HTML-based برای استخراج محتوای پاک
                    const allText = extractCleanMasaelContent(container);
                    const hasMasaelContent = allText.includes('Məsələ') && allText.includes(parentText);

                    debug.push('Has masael content: ' + hasMasaelContent);
                    debug.push('Text preview: ' + allText.substring(0, 200));

                    if (hasMasaelContent) {{
                        // استخراج محتوای masael از container - با فیلتر HTML
                        const masaelContent = allText.substring(0, 2000); // محدودیت 2000 کاراکتر

                        children.push({{
                            text: parentText,
                            tagName: 'H5',
                            className: 'matn',
                            element_type: 'masael',
                            has_masael_content: true,
                            hasChildren: false,
                            content_preview: masaelContent,
                            is_self: true,
                            children: []
                        }});

                        debug.push('Added self as first masael child');
                    }}

                    // جستجو برای فرزندان مستقیم فقط در container
                    const directChildElements = container.querySelectorAll('h5.tree_expand');
                    debug.push('Direct child h5.tree_expand elements found: ' + directChildElements.length);

                    const addedTexts = new Set(); // جلوگیری از تکرار در همین سطح

                    for (let element of directChildElements) {{
                        const text = element.textContent ? element.textContent.trim() : '';

                        // فیلتر کردن عناصر نامناسب و تکراری
                        if (text.length > 3 && text.length < 100 &&
                            text !== parentText &&
                            !text.includes('Çap versiyası') &&
                            !text.includes('Leader.ir') &&
                            !text.includes('NAMAZ') &&
                            !text.includes('ORUC') &&
                            !addedTexts.has(text)) {{

                            children.push({{
                                text: text,
                                tagName: element.tagName,
                                className: element.className,
                                element_type: 'unknown', // باید بررسی شود
                                has_masael_content: false,
                                hasChildren: element.classList.contains('tree_expand')
                            }});

                            addedTexts.add(text);
                        }}
                    }}

                    debug.push('Children found: ' + children.length);
                    return {{ children, debug }};
                }}
            """, parent_text)

            # نمایش debug info
            if 'debug' in children_info:
                print(f"  Debug برای {parent_text}:")
                for debug_msg in children_info['debug']:
                    print(f"    {debug_msg}")
                children_info = children_info['children']

            # حالا روی هر فرزند کلیک کنیم تا نوعش را تشخیص دهیم
            for child in children_info:
                if child.get('hasChildren') and not child.get('is_self'):
                    # بررسی تکرار فرزند برای این والد
                    if self.is_duplicate_child(parent_text, child['text']):
                        print(f"    ⚠️  فرزند تکراری رد شد: {child['text'][:30]}... برای والد {parent_text[:30]}...")
                        continue

                    child_type_info = await self.analyze_element_type(child['text'])
                    if child_type_info:
                        child['has_masael_content'] = child_type_info['hasContent']
                        child['content_preview'] = child_type_info.get('contentPreview', '')

                        # تشخیص نوع بر اساس محتوا و فرزندان
                        if child_type_info['hasContent']:
                            # بررسی اینکه آیا این فرزند خودش فرزندان دارد یا نه
                            # محدود کردن عمق recursion برای جلوگیری از تکرار بی‌نهایت
                            if parent_text != child['text']:  # جلوگیری از recursion خودی
                                # اگر hasChildren true است، حتماً فرزندان را استخراج کنیم
                                if child.get('hasChildren', False):
                                    child_children = await self.extract_children(child['text'])

                                    # بررسی تعداد فرزندان واقعی (غیر از خودش)
                                    real_children = [c for c in child_children if c.get('text') != child['text']]

                                    if len(real_children) > 0:  # دارای فرزندان واقعی
                                        # دسته‌بندی با masael
                                        child['element_type'] = 'category'
                                        child['children'] = child_children
                                        print(f"      فرزند {child['text'][:30]}... تبدیل به دسته‌بندی با {len(real_children)} فرزند واقعی")
                                    else:
                                        # فقط masael - hasChildren اشتباه بوده
                                        child['element_type'] = 'masael'
                                        child['children'] = []
                                        print(f"      فرزند {child['text'][:30]}... masael (hasChildren اشتباه بود)")
                                else:
                                    # فقط masael - بدون فرزندان
                                    child['element_type'] = 'masael'
                                    child['children'] = []
                            else:
                                # جلوگیری از recursion خودی
                                child['element_type'] = 'masael'
                                child['children'] = []  # اطمینان از وجود children key
                                print(f"      فرزند {child['text'][:30]}... جلوگیری از recursion خودی")
                        else:
                            # دسته‌بندی بدون masael - باید فرزندانش را استخراج کنیم
                            child['element_type'] = 'category'
                            if parent_text != child['text']:  # جلوگیری از recursion خودی
                                child_children = await self.extract_children(child['text'])
                                child['children'] = child_children
                                print(f"      فرزند {child['text'][:30]}... دسته‌بندی با {len(child_children)} فرزند")
                            else:
                                child['children'] = []
                                print(f"      فرزند {child['text'][:30]}... جلوگیری از recursion خودی")
                    else:
                        # اگر analyze_element_type ناموفق بود، همچنان باید فرزندان را استخراج کنیم
                        child['has_masael_content'] = False
                        child['content_preview'] = ''
                        if parent_text != child['text']:  # جلوگیری از recursion خودی
                            child_children = await self.extract_children(child['text'])
                            child['children'] = child_children

                            # بررسی تعداد فرزندان واقعی برای تشخیص نوع
                            real_children = [c for c in child_children if c.get('text') != child['text'] or not c.get('is_self', False)]

                            if len(real_children) > 0:
                                child['element_type'] = 'category'
                                print(f"      فرزند {child['text'][:30]}... دسته‌بندی (پیش‌فرض) با {len(real_children)} فرزند واقعی")
                            else:
                                child['element_type'] = 'other'
                                child['children'] = []
                                print(f"      فرزند {child['text'][:30]}... other (پیش‌فرض، بدون فرزندان)")
                        else:
                            child['element_type'] = 'other'
                            child['children'] = []

                    print(f"    فرزند {child['text'][:30]}...: نوع = {child['element_type']}")

            # اعتبارسنجی ساختار سلسله‌مراتبی
            validated_children = self.validate_hierarchical_structure(children_info, parent_text)

            # ذخیره در cache
            self.extraction_cache[parent_text] = validated_children
            return validated_children

        except Exception as e:
            print(f"خطا در استخراج فرزندان {parent_text}: {e}")
            # ذخیره نتیجه خطا در cache
            self.extraction_cache[parent_text] = []
            return []

    def add_masael_self_reference(self, category_obj):
        """
        برای دسته‌بندی‌هایی که دارای محتوای masael هستند، یک آبجکت masael خودارجاعی ایجاد می‌کند
        و آن را به عنوان اولین فرزند اضافه می‌کند
        """
        # بررسی شرایط: element_type باید category باشد و has_masael_content باید true باشد
        if (category_obj.get('element_type') == 'category' and
            category_obj.get('has_masael_content') == True and
            category_obj.get('content_preview', '').strip()):

            # بررسی وجود کلمه کلیدی "Məsələ" در محتوا (case-sensitive)
            content_preview = category_obj.get('content_preview', '')
            if 'Məsələ' in content_preview:

                # بررسی اینکه آیا قبلاً masael خودارجاعی اضافه شده است یا نه
                children = category_obj.get('children', [])
                has_self_masael = any(
                    child.get('text') == category_obj.get('text') and
                    child.get('element_type') == 'masael' and
                    child.get('is_self') == True
                    for child in children
                )

                if not has_self_masael:
                    # ایجاد آبجکت masael خودارجاعی
                    self_masael = {
                        'text': category_obj.get('text'),
                        'tagName': 'H5',
                        'className': 'matn',
                        'element_type': 'masael',
                        'has_masael_content': True,
                        'hasChildren': False,
                        'content_preview': content_preview,
                        'is_self': True,
                        'children': []
                    }

                    # اضافه کردن به عنوان اولین فرزند
                    category_obj['children'] = [self_masael] + children

                    print(f"  ✅ اضافه شد masael خودارجاعی برای: {category_obj.get('text', '')[:50]}...")
                    return True
                else:
                    print(f"  ⚠️  masael خودارجاعی قبلاً موجود است برای: {category_obj.get('text', '')[:50]}...")
                    return False
            else:
                print(f"  ⚠️  کلمه کلیدی 'Məsələ' یافت نشد در: {category_obj.get('text', '')[:50]}...")
                return False
        else:
            # شرایط برآورده نشده
            return False

    def process_masael_self_references(self, items_list):
        """
        لیست آیتم‌ها را پردازش کرده و برای دسته‌بندی‌های مناسب masael خودارجاعی اضافه می‌کند
        این تابع به صورت بازگشتی روی تمام سطوح کار می‌کند
        """
        processed_count = 0

        for item in items_list:
            # پردازش آیتم فعلی
            if self.add_masael_self_reference(item):
                processed_count += 1

            # پردازش بازگشتی فرزندان
            if item.get('children'):
                child_processed = self.process_masael_self_references(item['children'])
                processed_count += child_processed

        return processed_count

    def extract_masael_content(self, masael_obj):
        """
        استخراج محتوای masael از یک شیء masael و ساختاردهی آن

        Args:
            masael_obj: شیء masael که باید محتوای آن پردازش شود

        Returns:
            bool: True اگر محتوا با موفقیت استخراج شد، False در غیر این صورت
        """
        import re

        # بررسی اولیه
        if masael_obj.get('element_type') != 'masael':
            return False

        content_preview = masael_obj.get('content_preview', '')
        if not content_preview:
            masael_obj['masael_content'] = []
            masael_obj['masael_count'] = 0
            return True

        try:
            # پاک‌سازی محتوا از artifacts
            cleaned_content = self.clean_content_for_masael_extraction(content_preview)

            # الگوی regex برای تشخیص masael headers
            masael_pattern = r'Məsələ\s+(\d+)\s*\.'

            # پیدا کردن تمام masael headers
            matches = list(re.finditer(masael_pattern, cleaned_content))

            if not matches:
                masael_obj['masael_content'] = []
                masael_obj['masael_count'] = 0
                return True

            masael_entries = []

            for i, match in enumerate(matches):
                masael_number = int(match.group(1))
                header = match.group(0)
                start_pos = match.end()

                # تعیین پایان محتوای این masael
                if i + 1 < len(matches):
                    end_pos = matches[i + 1].start()
                else:
                    end_pos = len(cleaned_content)

                # استخراج متن masael
                masael_text = cleaned_content[start_pos:end_pos].strip()

                # پاک‌سازی نهایی متن
                masael_text = self.clean_masael_text(masael_text)

                if masael_text:  # فقط اگر متن غیر خالی باشد
                    masael_entry = {
                        'masael_number': masael_number,
                        'header': header,
                        'text': masael_text
                    }
                    masael_entries.append(masael_entry)

            # اضافه کردن فیلدهای جدید
            masael_obj['masael_content'] = masael_entries
            masael_obj['masael_count'] = len(masael_entries)

            return True

        except Exception as e:
            print(f"⚠️  خطا در استخراج محتوای masael برای '{masael_obj.get('text', 'Unknown')[:30]}...': {e}")
            masael_obj['masael_content'] = []
            masael_obj['masael_count'] = 0
            return False

    def clean_content_for_masael_extraction(self, content):
        """
        پاک‌سازی محتوا برای استخراج masael - نسخه بهبود یافته
        """
        import re

        if not content:
            return ""

        # حذف headers مشترک اما حفظ محتوای اصلی
        content = re.sub(r'.*?Çap versiyası\s*;\s*PDF\s*\n?', '', content, flags=re.DOTALL)

        # حذف navigation paths
        content = re.sub(r'NAMAZ\s*/[^\n]*\n?', '', content)

        # حذف عنوان تکراری در ابتدا (فقط اولین خط عنوان)
        lines = content.split('\n')
        cleaned_lines = []
        title_removed = False

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # اگر خط شامل "Məsələ" است، همه محتوا را حفظ کن
            if 'Məsələ' in line and re.search(r'Məsələ\s+\d+', line):
                cleaned_lines.append(line)
            elif cleaned_lines:  # اگر قبلاً masael پیدا شده، همه خطوط بعدی را حفظ کن
                cleaned_lines.append(line)
            elif not title_removed:
                # اولین خط غیر خالی که masael نیست را به عنوان عنوان حذف کن
                title_removed = True
                continue
            else:
                # سایر خطوط را حفظ کن
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def clean_masael_text(self, text):
        """
        پاک‌سازی نهایی متن masael - نسخه محافظه‌کارانه با حذف footnotes
        """
        import re

        if not text:
            return ""

        # حذف artifacts مشترک فقط در ابتدای متن
        text = re.sub(r'^.*?Çap versiyası.*?PDF\s*', '', text, flags=re.DOTALL)
        text = re.sub(r'^.*?NAMAZ\s*/[^\n]*\n?', '', text, flags=re.MULTILINE)

        # حذف footnotes - الگوهای مختلف footnote
        text = self.remove_footnotes(text)

        # نرمال‌سازی whitespace اما حفظ ساختار
        text = re.sub(r'[ \t]+', ' ', text)  # فقط spaces و tabs را نرمال کن
        text = re.sub(r'\n\s*\n', '\n', text)  # خطوط خالی اضافی را حذف کن

        # حذف فاصله‌های اضافی در ابتدا و انتها
        text = text.strip()

        return text

    def remove_footnotes(self, text):
        """
        حذف footnotes از متن masael - نسخه کامل و بهبود یافته

        Args:
            text: متن اصلی که ممکن است شامل footnote باشد

        Returns:
            str: متن پاک شده از footnotes
        """
        import re

        if not text:
            return ""

        # مرحله 1: حذف footnote references [1], [2], etc.
        text = re.sub(r'\[\d+\]', '', text)

        # مرحله 2: تشخیص و حذف footnote content که با کلمات مشخص شروع می‌شود
        footnote_start_patterns = [
            r'\s+Bu,\s+',  # " Bu, ..."
            r'\s+Əslində,\s+',  # " Əslində, ..."
            r'\s+Yəni\s+',  # " Yəni ..."
            r'\s+Məsələn\s+',  # " Məsələn ..."
        ]

        for pattern in footnote_start_patterns:
            text = re.sub(pattern + r'.*$', '.', text, flags=re.DOTALL)

        # مرحله 3: حذف جملات footnote که در خطوط جداگانه هستند
        lines = text.split('\n')
        main_lines = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # اگر خط با الگوهای footnote شروع می‌شود، آن را نادیده بگیر
            if re.match(r'^(Bu|Əslində|Yəni|Məsələn|Lakin|Ancaq)', line):
                continue

            main_lines.append(line)

        # مرحله 4: حذف جملات footnote که در وسط متن هستند
        result = ' '.join(main_lines)

        # تقسیم به جملات و فیلتر کردن footnote content
        sentences = re.split(r'(?<=[.!?])\s+', result)
        main_sentences = []

        for sentence in sentences:
            sentence = sentence.strip()
            if not sentence:
                continue

            # بررسی اینکه آیا جمله footnote content است
            is_footnote = (
                # جملات که با کلمات footnote شروع می‌شوند
                re.match(r'^(Bu|Əslində|Yəni|Məsələn|Lakin|Ancaq|Həmçinin)', sentence) or
                # جملات کوتاه که احتمالاً footnote هستند
                (len(sentence) < 30 and re.search(r'(vacibdir|lazımdir|məlumdur|var)\.?$', sentence)) or
                # جملات که فقط شامل یک کلمه یا دو کلمه هستند
                len(sentence.split()) <= 3
            )

            if not is_footnote:
                main_sentences.append(sentence)

        # مرحله 5: ترکیب جملات اصلی
        if main_sentences:
            result = '. '.join(main_sentences)
            if not result.endswith('.'):
                result += '.'
        else:
            # اگر هیچ جمله اصلی پیدا نشد، متن اصلی را برگردان (بدون footnote references)
            result = text

        # مرحله 6: پاک‌سازی نهایی
        result = re.sub(r'\s+', ' ', result)
        result = re.sub(r'\.+', '.', result)
        result = result.strip()

        return result


    def process_masael_content_extraction(self, items_list):
        """
        پردازش بازگشتی برای استخراج محتوای masael از تمام آیتم‌ها

        Args:
            items_list: لیست آیتم‌ها برای پردازش

        Returns:
            int: تعداد آیتم‌های masael پردازش شده
        """
        processed_count = 0

        for item in items_list:
            # پردازش آیتم فعلی اگر masael است
            if item.get('element_type') == 'masael':
                if self.extract_masael_content(item):
                    processed_count += 1

            # پردازش بازگشتی فرزندان
            children = item.get('children', [])
            if children:
                processed_count += self.process_masael_content_extraction(children)

        return processed_count

    async def save_structure(self, filename="improved_structure1.json"):
        """ساختار را در فایل JSON ذخیره می‌کند"""
        print(f"\nدر حال ذخیره ساختار...")
        print(f"تعداد دسته‌بندی‌های اصلی: {len(self.structure)}")

        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.structure, f, ensure_ascii=False, indent=2)
        print(f"ساختار در فایل {filename} ذخیره شد")
        
    async def close(self):
        """مرورگر را می‌بندد"""
        if self.browser:
            await self.browser.close()

async def main(max_items=None):
    """تابع اصلی"""
    print(f"🚀 شروع تحلیل سایت leader.ir")
    if max_items:
        print(f"📊 محدودیت تعداد: {max_items} آیتم")
    else:
        print(f"📊 بدون محدودیت تعداد")

    analyzer = ImprovedLeaderAnalyzer(max_items=max_items)

    try:
        await analyzer.init_browser()
        await analyzer.navigate_to_site()

        # پیدا کردن و کلیک روی دسته‌بندی‌ها
        categories = await analyzer.find_and_click_categories()

        # پردازش پس از استخراج: اضافه کردن masael خودارجاعی برای دسته‌بندی‌ها
        print(f"\n🔄 شروع پردازش پس از استخراج...")
        total_processed = 0
        for cat_name, cat_data in analyzer.structure.items():
            subcategories = cat_data.get('subcategories', [])
            if subcategories:
                processed_count = analyzer.process_masael_self_references(subcategories)
                total_processed += processed_count
                print(f"  📂 {cat_name}: {processed_count} دسته‌بندی پردازش شد")

        print(f"✅ پردازش پس از استخراج کامل شد!")
        print(f"📊 تعداد کل masael خودارجاعی اضافه شده: {total_processed}")

        # پردازش استخراج محتوای masael
        print(f"\n🔍 شروع استخراج محتوای masael...")
        total_masael_processed = 0
        total_masael_entries = 0

        for cat_name, cat_data in analyzer.structure.items():
            subcategories = cat_data.get('subcategories', [])
            if subcategories:
                masael_processed = analyzer.process_masael_content_extraction(subcategories)
                total_masael_processed += masael_processed

                # شمارش کل masael entries
                def count_masael_entries(items_list):
                    count = 0
                    for item in items_list:
                        if item.get('element_type') == 'masael':
                            count += item.get('masael_count', 0)
                        children = item.get('children', [])
                        if children:
                            count += count_masael_entries(children)
                    return count

                entries_count = count_masael_entries(subcategories)
                total_masael_entries += entries_count
                print(f"  📂 {cat_name}: {masael_processed} masael پردازش شد، {entries_count} مسئله استخراج شد")

        print(f"✅ استخراج محتوای masael کامل شد!")
        print(f"📊 تعداد کل masael پردازش شده: {total_masael_processed}")
        print(f"📊 تعداد کل مسائل استخراج شده: {total_masael_entries}")

        # ذخیره ساختار اصلی
        await analyzer.save_structure()

        # ذخیره ساختار با محتوای masael
        await analyzer.save_structure("improved_structure_with_masael.json")

        print(f"\n✅ تحلیل کامل شد!")
        print(f"📊 تعداد آیتم‌های پردازش شده: {analyzer.processed_items}")

        print("\n=== خلاصه نتایج ===")
        for cat_name, cat_data in analyzer.structure.items():
            print(f"{cat_name}: {cat_data['total_categories']} دسته‌بندی، {cat_data['total_children']} فرزند")

    except Exception as e:
        print(f"❌ خطا رخ داد: {e}")
        import traceback
        traceback.print_exc()

    finally:
        await analyzer.close()

if __name__ == "__main__":
    # تنظیم محدودیت تعداد اینجا
    MAX_ITEMS = None # تغییر این عدد برای تنظیم محدودیت

    print(f"🎯 تنظیمات:")
    print(f"   - حداکثر آیتم‌ها: {MAX_ITEMS}")
    print(f"   - برای تغییر، عدد MAX_ITEMS را در کد تغییر دهید")
    print(f"   - برای حذف محدودیت، MAX_ITEMS = None قرار دهید")

    asyncio.run(main(max_items=MAX_ITEMS))
