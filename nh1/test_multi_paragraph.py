#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست خطبه‌ای با چندین پاراگراف
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_multi_paragraph_speech():
    """تست خطبه‌ای که احتمالاً چندین پاراگراف دارد"""
    
    scraper = NahjScraper()
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # تست خطبه 1 که معمولاً پاراگراف‌های زیادی دارد
            speech_info = {
                "number": 1,
                "page": 1,
                "title": "خطبه 1 - آغاز آفرینش آسمان و زمین آدم",
                "url": "http://nahj.makarem.ir/speech/1",
                "total_section_phrase_container": 0,
                "containers": []
            }
            
            logger.info("🧪 تست خطبه با چندین پاراگراف...")
            
            # استخراج محتوا
            result = await scraper.extract_speech_content(page, speech_info)
            
            # نمایش نتایج
            print(f"\n📖 خطبه {result['number']}: {result['title']}")
            print(f"📊 تعداد بخش‌ها: {result['total_section_phrase_container']}")
            
            # بررسی اولین بخش که احتمالاً چندین پاراگراف دارد
            if result['containers']:
                container = result['containers'][0]
                print(f"\n--- بخش {container['number']}: {container['header']} ---")
                
                # نمایش ساختار arabic-text
                arabic = container['arabic-text']
                print(f"🔤 متن عربی:")
                print(f"   📝 Main ({len(arabic['main'])} کاراکتر): {arabic['main'][:100]}...")
                if arabic['metadata']:
                    print(f"   📋 Metadata ({len(arabic['metadata'])} پاراگراف):")
                    for i, meta in enumerate(arabic['metadata'], 1):
                        print(f"      {i}. ({len(meta)} کاراکتر): {meta[:50]}...")
                else:
                    print(f"   📋 Metadata: None")
                
                # نمایش ساختار translate-text
                translate = container['translate-text']
                print(f"🔤 متن ترجمه:")
                print(f"   📝 Main ({len(translate['main'])} کاراکتر): {translate['main'][:100]}...")
                if translate['metadata']:
                    print(f"   📋 Metadata ({len(translate['metadata'])} پاراگراف):")
                    for i, meta in enumerate(translate['metadata'], 1):
                        print(f"      {i}. ({len(meta)} کاراکتر): {meta[:50]}...")
                else:
                    print(f"   📋 Metadata: None")
            
            # ذخیره نمونه JSON
            with open('test_multi_paragraph.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ تست موفقیت‌آمیز! نتیجه در test_multi_paragraph.json ذخیره شد.")
            
        finally:
            await browser.close()

async def main():
    print("🚀 شروع تست خطبه با چندین پاراگراف")
    
    await test_multi_paragraph_speech()
    
    print("\n🎉 تست تکمیل شد!")

if __name__ == "__main__":
    asyncio.run(main())
