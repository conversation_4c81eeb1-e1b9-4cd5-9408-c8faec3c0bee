#!/usr/bin/env python3
"""
تست راه‌حل برای خطبه 723
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_speech_723_fix():
    """تست راه‌حل برای خطبه 723"""
    
    speech_info = {
        "number": 723,
        "url": "http://nahj.makarem.ir/speech/723",
        "title": "خطبه 224 - پارسائي علي و سرگذشت عقيل"
    }
    
    scraper = NahjScraper("speech")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info(f"تست خطبه {speech_info['number']}: {speech_info['title']}")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, speech_info)
            
            print("\n" + "="*80)
            print(f"نتایج استخراج خطبه {speech_info['number']}:")
            print("="*80)
            
            if result['containers']:
                print(f"تعداد کانتینرها: {len(result['containers'])}")
                
                for i, container in enumerate(result['containers'], 1):
                    arabic_text = container.get('arabic-text', {}).get('main', '')
                    translate_text = container.get('translate-text', {}).get('main', '')
                    header = container.get('header', '')
                    
                    print(f"\nکانتینر {i}:")
                    print(f"  Header: '{header}'")
                    print(f"  عربی: {len(arabic_text)} کاراکتر")
                    print(f"  ترجمه: {len(translate_text)} کاراکتر")
                    
                    if i == 1:  # بررسی ویژه کانتینر اول
                        print(f"  🔍 کانتینر اول:")
                        print(f"     عربی: '{arabic_text[:50]}...'")
                        print(f"     ترجمه: '{translate_text[:50]}...'")
                        
                        if translate_text:
                            print(f"  ✅ مشکل کانتینر اول حل شد!")
                        else:
                            print(f"  ❌ مشکل کانتینر اول هنوز وجود دارد!")
                
                # بررسی کلی
                first_container = result['containers'][0]
                first_translate = first_container.get('translate-text', {}).get('main', '')
                
                if first_translate:
                    print(f"\n🎉 تست موفق: کانتینر اول دارای ترجمه است ({len(first_translate)} کاراکتر)")
                    return True
                else:
                    print(f"\n❌ تست ناموفق: کانتینر اول هنوز بدون ترجمه است")
                    return False
            else:
                print("❌ هیچ کانتینری یافت نشد")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطا در تست: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
            return False
        
        finally:
            await browser.close()

if __name__ == "__main__":
    result = asyncio.run(test_speech_723_fix())
    if result:
        print("\n🎯 راه‌حل موفق بود! کد آماده برای اعمال در اسکریپت اصلی است.")
    else:
        print("\n⚠️ راه‌حل نیاز به بررسی بیشتر دارد.")
