#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت استخراج خطبه‌ها و حکمت‌های نهج البلاغه از سایت nahj.makarem.ir
نویسنده: AI Assistant
"""

import asyncio
import json
import os
import sys
import time
from typing import Dict, List, Any
from playwright.async_api import async_playwright, Page, Browser
import logging

# تنظیم لاگ
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('nahj_scraper.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class NahjScraper:
    def __init__(self, content_type="speech"):
        self.base_url = "http://nahj.makarem.ir"
        self.content_type = content_type

        if content_type == "wisdom":
            self.list_url = f"{self.base_url}/wisdom"
            self.total_pages = 20
            self.data = {
                "information": {
                    "total_wisdom": 0,
                    "total_pages": 20,
                    "total_section_containers": 0,
                },
                "wisdoms": []
            }
            self.output_file = "nahj_wisdoms.json"
        elif content_type == "letter":
            self.list_url = f"{self.base_url}/letter"
            self.total_pages = 4
            self.data = {
                "information": {
                    "total_letter": 0,
                    "total_pages": 4,
                    "total_section_containers": 0,
                },
                "letters": []
            }
            self.output_file = "nahj_letters.json"
        else:  # speech
            self.list_url = f"{self.base_url}/speech"
            self.total_pages = 11
            self.data = {
                "information": {
                    "total_speech": 0,
                    "total_pages": 11,
                    "total_section_containers": 0,
                },
                "speechs": []
            }
            self.output_file = "nahj_speeches.json"
        
    def structure_paragraphs(self, paragraphs):
        """ساختاردهی پاراگراف‌ها به صورت main و metadata"""
        if not paragraphs:
            return {"main": "", "metadata": None}

        if len(paragraphs) == 1:
            return {"main": paragraphs[0], "metadata": None}

        # پیدا کردن طولانی‌ترین پاراگراف به عنوان main
        main_paragraph = max(paragraphs, key=len)
        metadata_paragraphs = [p for p in paragraphs if p != main_paragraph]

        return {
            "main": main_paragraph,
            "metadata": metadata_paragraphs if metadata_paragraphs else None
        }

    async def save_data(self):
        """ذخیره داده‌ها در فایل JSON"""
        try:
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(self.data, f, ensure_ascii=False, indent=2)
            logger.info(f"داده‌ها در فایل {self.output_file} ذخیره شد")
        except Exception as e:
            logger.error(f"خطا در ذخیره فایل: {e}")
    
    async def load_existing_data(self):
        """بارگذاری داده‌های موجود در صورت وجود"""
        if os.path.exists(self.output_file):
            try:
                with open(self.output_file, 'r', encoding='utf-8') as f:
                    self.data = json.load(f)
                logger.info(f"داده‌های موجود از فایل {self.output_file} بارگذاری شد")

                if self.content_type == "wisdom":
                    logger.info(f"تعداد حکمت‌های موجود: {len(self.data['wisdoms'])}")
                elif self.content_type == "letter":
                    logger.info(f"تعداد نامه‌های موجود: {len(self.data['letters'])}")
                else:
                    logger.info(f"تعداد خطبه‌های موجود: {len(self.data['speechs'])}")
            except Exception as e:
                logger.error(f"خطا در بارگذاری فایل موجود: {e}")
    
    async def get_content_list_from_page(self, page: Page, page_num: int) -> List[Dict]:
        """استخراج لیست محتوا (خطبه یا حکمت) از یک صفحه"""
        contents = []

        if page_num == 1:
            url = self.list_url
        else:
            url = f"{self.list_url}/page/{page_num}"

        content_name = "حکمت" if self.content_type == "wisdom" else "خطبه"
        logger.info(f"در حال استخراج {content_name}‌ها از صفحه {page_num}: {url}")

        try:
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(1000)  # انتظار بیشتر

            # استخراج کارت‌های محتوا
            content_cards = await page.query_selector_all('.phrase-card')
            logger.info(f"تعداد کارت‌های یافت شده در صفحه {page_num}: {len(content_cards)}")

            for card in content_cards:
                try:
                    # استخراج عنوان و لینک
                    title_element = await card.query_selector('h5.card-title a')
                    if title_element:
                        title = await title_element.inner_text()
                        href = await title_element.get_attribute('href')

                        if href and title:
                            # استخراج شماره از لینک
                            content_number = href.split('/')[-1]

                            content_info = {
                                "number": int(content_number),
                                "page": page_num,
                                "title": title.strip(),
                                "url": f"{self.base_url}{href}",
                                "total_section_phrase_container": 0,
                                "containers": []
                            }
                            contents.append(content_info)
                            logger.debug(f"{content_name} استخراج شد: {title}")

                except Exception as e:
                    logger.error(f"خطا در استخراج کارت {content_name}: {e}")
                    continue

        except Exception as e:
            logger.error(f"خطا در بارگذاری صفحه {page_num}: {e}")

        return contents
    
    def process_text_content(self, text_elements: list) -> dict:
        """پردازش محتوای متنی و تبدیل به ساختار جدید"""
        if not text_elements:
            return {"main": "", "metadata": None}

        # استخراج متن از هر المنت
        paragraphs = []
        for element in text_elements:
            text = element.strip()
            if text:
                paragraphs.append(text)

        if not paragraphs:
            return {"main": "", "metadata": None}

        if len(paragraphs) == 1:
            # فقط یک پاراگراف
            return {"main": paragraphs[0], "metadata": None}
        else:
            # چندین پاراگراف - پیدا کردن بیشترین کاراکتر
            main_paragraph = max(paragraphs, key=len)
            metadata_paragraphs = [p for p in paragraphs if p != main_paragraph]

            return {
                "main": main_paragraph,
                "metadata": metadata_paragraphs if metadata_paragraphs else None
            }

    async def extract_content(self, page: Page, content_info: Dict) -> Dict:
        """استخراج محتوای یک خطبه یا حکمت"""
        content_name = "حکمت" if self.content_type == "wisdom" else ("نامه" if self.content_type == "letter" else "خطبه")
        logger.info(f"در حال استخراج محتوای {content_name} {content_info['number']}: {content_info['title']}")

        try:
            await page.goto(content_info['url'], wait_until='networkidle')
            await page.wait_for_timeout(2000)  # انتظار بیشتر برای بارگذاری کامل

            containers = []

            if self.content_type == "wisdom":
                # برای حکمت‌ها: استخراج از HTML elements مشخص
                try:
                    arabic_text = ""
                    translate_text = ""

                    # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
                    main_content_div = await page.query_selector('.col-lg-7.col-sm-11')

                    if main_content_div:
                        # استخراج متن عربی از p با کلاس card-text arabic-text
                        arabic_element = await main_content_div.query_selector('p.card-text.arabic-text')
                        if arabic_element:
                            # استخراج HTML برای حفظ ساختار
                            arabic_html = await arabic_element.inner_html()
                            # تمیز کردن HTML و تبدیل <br> به \n
                            arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                            # حذف سایر تگ‌های HTML
                            import re
                            arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                            # تمیز کردن فضاهای اضافی
                            arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())

                        # استخراج متن ترجمه از p با کلاس card-text translate-text
                        translate_element = await main_content_div.query_selector('p.card-text.translate-text')
                        if translate_element:
                            # استخراج HTML برای حفظ ساختار
                            translate_html = await translate_element.inner_html()
                            # تمیز کردن HTML و تبدیل <br> به \n
                            translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                            # حذف سایر تگ‌های HTML (به جز لینک‌های پاورقی)
                            import re
                            # حذف لینک‌های پاورقی
                            translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                            # حذف سایر تگ‌های HTML
                            translate_text = re.sub(r'<[^>]+>', '', translate_text)
                            # تمیز کردن فضاهای اضافی
                            translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())

                    # اگر روش اول کار نکرد، از روش‌های جایگزین استفاده کن
                    if not arabic_text or not translate_text:
                        logger.debug(f"روش جایگزین برای حکمت {content_info['number']} - عربی: {bool(arabic_text)}, ترجمه: {bool(translate_text)}")

                        # روش 2: جستجو مستقیم برای عناصر
                        if not arabic_text:
                            arabic_elements = await page.query_selector_all('p.card-text.arabic-text')
                            if arabic_elements:
                                arabic_texts = []
                                for element in arabic_elements:
                                    text = await element.inner_text()
                                    if text.strip():
                                        arabic_texts.append(text.strip())
                                arabic_text = '\n'.join(arabic_texts)
                                logger.debug(f"متن عربی از روش 2: {len(arabic_text)} کاراکتر")

                        if not translate_text:
                            translate_elements = await page.query_selector_all('p.card-text.translate-text')
                            if translate_elements:
                                translate_texts = []
                                for element in translate_elements:
                                    text = await element.inner_text()
                                    if text.strip():
                                        translate_texts.append(text.strip())
                                translate_text = '\n'.join(translate_texts)
                                logger.debug(f"متن ترجمه از روش 2: {len(translate_text)} کاراکتر")

                        # روش 3: جستجو با CSS selector های مختلف
                        if not arabic_text:
                            selectors_to_try = [
                                '.arabic-text',
                                'p[class*="arabic"]',
                                'p[style*="20pt"]',
                                '.card-text.arabic-text'
                            ]

                            for selector in selectors_to_try:
                                elements = await page.query_selector_all(selector)
                                if elements:
                                    for element in elements:
                                        text = await element.inner_text()
                                        if text.strip() and any('\u0600' <= char <= '\u06FF' for char in text):
                                            arabic_text = text.strip()
                                            logger.debug(f"متن عربی از selector {selector}: {len(arabic_text)} کاراکتر")
                                            break
                                if arabic_text:
                                    break

                        if not translate_text:
                            translate_selectors = [
                                '.translate-text',
                                'p[class*="translate"]',
                                'p[style*="14pt"]',
                                '.card-text.translate-text'
                            ]

                            for selector in translate_selectors:
                                elements = await page.query_selector_all(selector)
                                if elements:
                                    for element in elements:
                                        text = await element.inner_text()
                                        if text.strip() and 'امام' in text:
                                            translate_text = text.strip()
                                            logger.debug(f"متن ترجمه از selector {selector}: {len(translate_text)} کاراکتر")
                                            break
                                if translate_text:
                                    break

                    if arabic_text or translate_text:
                        container_data = {
                            "number": 1,
                            "header": "",
                            "arabic-text": {"main": arabic_text, "metadata": None},
                            "translate-text": {"main": translate_text, "metadata": None}
                        }
                        containers.append(container_data)
                        logger.debug(f"حکمت استخراج شد - عربی: {len(arabic_text)} کاراکتر، ترجمه: {len(translate_text)} کاراکتر")
                    else:
                        logger.warning(f"هیچ محتوایی برای حکمت {content_info['number']} یافت نشد")

                except Exception as e:
                    logger.error(f"خطا در استخراج حکمت: {e}")
                    import traceback
                    logger.error(f"جزئیات خطا: {traceback.format_exc()}")

            elif self.content_type == "letter":
                # برای نامه‌ها: مشابه خطبه‌ها اما با ساختار متفاوت
                try:
                    # جستجو برای کانتینرهای phrase-text-container
                    section_containers = await page.query_selector_all('.container-fluid article')

                    for i, container in enumerate(section_containers, 1):
                        try:
                            # استخراج header
                            header_element = await container.query_selector('h4')
                            header = await header_element.inner_text() if header_element else ""

                            # استخراج تمام پاراگراف‌های متن عربی
                            arabic_elements = await container.query_selector_all('.arabic-text p, .arabic-text')
                            arabic_texts = []
                            for elem in arabic_elements:
                                text = await elem.inner_text()
                                if text.strip():
                                    arabic_texts.append(text.strip())

                            # استخراج تمام پاراگراف‌های متن ترجمه
                            translate_elements = await container.query_selector_all('.translate-text p, .translate-text')
                            translate_texts = []
                            for elem in translate_elements:
                                text = await elem.inner_text()
                                if text.strip():
                                    translate_texts.append(text.strip())

                            # اگر ترجمه یافت نشد، جستجو برای p بعد از translate-text خالی
                            if not translate_texts:
                                phrase_container = await container.query_selector('.phrase-text-container')
                                if phrase_container:
                                    # پیدا کردن translate-text خالی
                                    translate_element = await phrase_container.query_selector('p.translate-text')
                                    if translate_element:
                                        # گرفتن تمام p های بعد از translate-text
                                        all_p_elements = await phrase_container.query_selector_all('p')
                                        translate_found = False
                                        for elem in all_p_elements:
                                            class_attr = await elem.get_attribute('class')
                                            # اگر به translate-text رسیدیم، فلگ را فعال کن
                                            if class_attr == 'translate-text':
                                                translate_found = True
                                                continue
                                            # اگر بعد از translate-text هستیم و p بدون کلاس است
                                            if translate_found and (not class_attr or class_attr == 'None'):
                                                text = await elem.inner_text()
                                                if text.strip():
                                                    translate_texts.append(text.strip())
                                                    logger.debug(f"متن ترجمه از p بعد از translate-text یافت شد: {len(text)} کاراکتر")
                                                    break  # فقط اولین p را بگیر

                            if arabic_texts or translate_texts:
                                arabic_data = self.structure_paragraphs(arabic_texts)
                                translate_data = self.structure_paragraphs(translate_texts)

                                container_data = {
                                    "number": i,
                                    "header": header,
                                    "arabic-text": arabic_data,
                                    "translate-text": translate_data
                                }
                                containers.append(container_data)
                                logger.debug(f"کانتینر {i} نامه استخراج شد")

                        except Exception as e:
                            logger.error(f"خطا در استخراج کانتینر {i} نامه: {e}")
                            continue

                except Exception as e:
                    logger.error(f"خطا در استخراج نامه: {e}")
                    import traceback
                    logger.error(f"جزئیات خطا: {traceback.format_exc()}")

            else:
                # برای خطبه‌ها: روش قبلی
                # روش اول: استخراج کانتینرهای بخش‌ها از .container-fluid article
                section_containers = await page.query_selector_all('.container-fluid article')
                logger.info(f"تعداد کانتینرهای یافت شده (روش اول): {len(section_containers)}")

                for i, container in enumerate(section_containers, 1):
                    try:
                        # استخراج header (h4)
                        header_element = await container.query_selector('h4')
                        header = await header_element.inner_text() if header_element else ""

                        # استخراج تمام پاراگراف‌های متن عربی
                        arabic_elements = await container.query_selector_all('.arabic-text p, .arabic-text')
                        arabic_texts = []
                        for elem in arabic_elements:
                            text = await elem.inner_text()
                            if text.strip():
                                arabic_texts.append(text.strip())

                        # استخراج تمام پاراگراف‌های متن ترجمه
                        translate_elements = await container.query_selector_all('.translate-text p, .translate-text')
                        translate_texts = []
                        for elem in translate_elements:
                            text = await elem.inner_text()
                            if text.strip():
                                translate_texts.append(text.strip())

                        # اگر ترجمه یافت نشد، جستجو برای p بعد از translate-text خالی
                        if not translate_texts:
                            phrase_container = await container.query_selector('.phrase-text-container')
                            if phrase_container:
                                # پیدا کردن translate-text خالی
                                translate_element = await phrase_container.query_selector('p.translate-text')
                                if translate_element:
                                    # گرفتن تمام p های بعد از translate-text
                                    all_p_elements = await phrase_container.query_selector_all('p')
                                    translate_found = False
                                    for elem in all_p_elements:
                                        class_attr = await elem.get_attribute('class')
                                        # اگر به translate-text رسیدیم، فلگ را فعال کن
                                        if class_attr == 'translate-text':
                                            translate_found = True
                                            continue
                                        # اگر بعد از translate-text هستیم و p بدون کلاس است
                                        if translate_found and (not class_attr or class_attr == 'None'):
                                            text = await elem.inner_text()
                                            if text.strip():
                                                translate_texts.append(text.strip())
                                                logger.debug(f"متن ترجمه از p بعد از translate-text یافت شد: {len(text)} کاراکتر")
                                                break  # فقط اولین p را بگیر

                        # پردازش محتوا با ساختار جدید
                        arabic_content = self.process_text_content(arabic_texts)
                        translate_content = self.process_text_content(translate_texts)

                        if header or arabic_content["main"] or translate_content["main"]:
                            container_data = {
                                "number": i,
                                "header": header.strip(),
                                "arabic-text": arabic_content,
                                "translate-text": translate_content
                            }
                            containers.append(container_data)
                            logger.debug(f"بخش {i} استخراج شد: {header}")

                    except Exception as e:
                        logger.error(f"خطا در استخراج بخش {i}: {e}")
                        continue

                # اگر هیچ کانتینری پیدا نشد، روش دوم را امتحان کن
                if len(containers) == 0:
                    logger.info("روش دوم: جستجوی مستقیم برای بخش‌ها")

                    # جستجو برای h4 های موجود در صفحه
                    h4_elements = await page.query_selector_all('h4')
                    logger.info(f"تعداد h4 های یافت شده: {len(h4_elements)}")

                    for i, h4_element in enumerate(h4_elements, 1):
                        try:
                            header = await h4_element.inner_text()

                            # پیدا کردن والد h4 که شامل محتوا است
                            parent = await h4_element.query_selector('xpath=..')
                            if not parent:
                                continue

                            # جستجو برای تمام المنت‌های متن عربی در والد
                            arabic_elements = await parent.query_selector_all('.arabic-text p, .arabic-text')
                            arabic_texts = []
                            for elem in arabic_elements:
                                text = await elem.inner_text()
                                if text.strip():
                                    arabic_texts.append(text.strip())

                            # جستجو برای تمام المنت‌های متن ترجمه در والد
                            translate_elements = await parent.query_selector_all('.translate-text p, .translate-text')
                            translate_texts = []
                            for elem in translate_elements:
                                text = await elem.inner_text()
                                if text.strip():
                                    translate_texts.append(text.strip())

                            # اگر ترجمه یافت نشد، جستجو برای p بعد از translate-text خالی
                            if not translate_texts:
                                phrase_container = await parent.query_selector('.phrase-text-container')
                                if phrase_container:
                                    # پیدا کردن translate-text خالی
                                    translate_element = await phrase_container.query_selector('p.translate-text')
                                    if translate_element:
                                        # گرفتن تمام p های بعد از translate-text
                                        all_p_elements = await phrase_container.query_selector_all('p')
                                        translate_found = False
                                        for elem in all_p_elements:
                                            class_attr = await elem.get_attribute('class')
                                            # اگر به translate-text رسیدیم، فلگ را فعال کن
                                            if class_attr == 'translate-text':
                                                translate_found = True
                                                continue
                                            # اگر بعد از translate-text هستیم و p بدون کلاس است
                                            if translate_found and (not class_attr or class_attr == 'None'):
                                                text = await elem.inner_text()
                                                if text.strip():
                                                    translate_texts.append(text.strip())
                                                    logger.debug(f"متن ترجمه از p بعد از translate-text یافت شد (روش 2): {len(text)} کاراکتر")
                                                    break  # فقط اولین p را بگیر

                            # اگر متن پیدا نشد، در کل صفحه جستجو کن
                            if not arabic_texts or not translate_texts:
                                # جستجو در تمام p های بعد از h4
                                next_elements = await page.evaluate('''(h4) => {
                                    const result = {arabic: [], translate: []};
                                    let current = h4.nextElementSibling;

                                    while (current) {
                                        if (current.classList.contains('arabic-text')) {
                                            const paragraphs = current.querySelectorAll('p');
                                            if (paragraphs.length > 0) {
                                                paragraphs.forEach(p => {
                                                    if (p.textContent.trim()) {
                                                        result.arabic.push(p.textContent.trim());
                                                    }
                                                });
                                            } else if (current.textContent.trim()) {
                                                result.arabic.push(current.textContent.trim());
                                            }
                                        } else if (current.classList.contains('translate-text')) {
                                            const paragraphs = current.querySelectorAll('p');
                                            if (paragraphs.length > 0) {
                                                paragraphs.forEach(p => {
                                                    if (p.textContent.trim()) {
                                                        result.translate.push(p.textContent.trim());
                                                    }
                                                });
                                            } else if (current.textContent.trim()) {
                                                result.translate.push(current.textContent.trim());
                                            }
                                        }
                                        current = current.nextElementSibling;
                                    }
                                    return result;
                                }''', h4_element)

                                if not arabic_texts and next_elements.get('arabic'):
                                    arabic_texts = next_elements['arabic']
                                if not translate_texts and next_elements.get('translate'):
                                    translate_texts = next_elements['translate']

                            # پردازش محتوا با ساختار جدید
                            arabic_content = self.process_text_content(arabic_texts)
                            translate_content = self.process_text_content(translate_texts)

                            if header and (arabic_content["main"] or translate_content["main"]):
                                container_data = {
                                    "number": i,
                                    "header": header.strip(),
                                    "arabic-text": arabic_content,
                                    "translate-text": translate_content
                                }
                                containers.append(container_data)
                                logger.debug(f"بخش {i} استخراج شد (روش دوم): {header}")

                        except Exception as e:
                            logger.error(f"خطا در استخراج بخش {i} (روش دوم): {e}")
                            continue

            content_info['containers'] = containers
            content_info['total_section_phrase_container'] = len(containers)

            logger.info(f"{content_name} {content_info['number']} با {len(containers)} بخش استخراج شد")

        except Exception as e:
            logger.error(f"خطا در استخراج محتوای {content_name} {content_info['number']}: {e}")

        return content_info
    
    async def run(self):
        """اجرای اصلی اسکریپت"""
        content_name = "حکمت" if self.content_type == "wisdom" else ("نامه" if self.content_type == "letter" else "خطبه")
        logger.info(f"شروع استخراج {content_name}‌های نهج البلاغه")

        # بارگذاری داده‌های موجود
        await self.load_existing_data()

        async with async_playwright() as p:
            # راه‌اندازی مرورگر با تنظیمات سریع
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-images',
                    '--disable-plugins',
                    '--disable-extensions'
                ]
            )
            page = await browser.new_page()

            # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())

            try:
                if self.content_type == "wisdom":
                    processed_contents = set(wisdom['number'] for wisdom in self.data['wisdoms'])
                    content_list_key = 'wisdoms'
                    total_key = 'total_wisdom'
                elif self.content_type == "letter":
                    processed_contents = set(letter['number'] for letter in self.data['letters'])
                    content_list_key = 'letters'
                    total_key = 'total_letter'
                else:
                    processed_contents = set(speech['number'] for speech in self.data['speechs'])
                    content_list_key = 'speechs'
                    total_key = 'total_speech'

                # پردازش هر صفحه به صورت جداگانه
                for page_num in range(1, self.total_pages + 1):
                    logger.info(f"🔄 پردازش صفحه {page_num}")

                    # استخراج لیست محتوا از این صفحه
                    contents = await self.get_content_list_from_page(page, page_num)
                    logger.info(f"📋 {len(contents)} {content_name} در صفحه {page_num} یافت شد")

                    page_contents = []

                    # استخراج محتوای این صفحه
                    for content_info in contents:
                        if content_info['number'] in processed_contents:
                            logger.info(f"⏭️ {content_name} {content_info['number']} قبلاً پردازش شده")
                            continue

                        logger.info(f"📖 استخراج {content_name} {content_info['number']}: {content_info['title'][:50]}...")

                        # استخراج محتوا
                        complete_content = await self.extract_content(page, content_info)
                        page_contents.append(complete_content)
                        processed_contents.add(content_info['number'])

                        # ذخیره بعد از هر محتوا برای جلوگیری از از دست رفتن داده‌ها
                        self.data[content_list_key].append(complete_content)
                        self.data['information'][total_key] = len(self.data[content_list_key])
                        self.data['information']['total_section_containers'] = sum(
                            content['total_section_phrase_container'] for content in self.data[content_list_key]
                        )
                        await self.save_data()
                        logger.info(f"💾 {content_name} {complete_content['number']} ذخیره شد")
                        page_contents.remove(complete_content)  # حذف از لیست محلی

                    # اضافه کردن محتوای این صفحه به داده‌ها
                    if page_contents:
                        self.data[content_list_key].extend(page_contents)

                        # به‌روزرسانی آمار
                        self.data['information'][total_key] = len(self.data[content_list_key])
                        self.data['information']['total_section_containers'] = sum(
                            content['total_section_phrase_container'] for content in self.data[content_list_key]
                        )

                        # ذخیره پس از تکمیل هر صفحه
                        await self.save_data()
                        logger.info(f"💾 صفحه {page_num} کامل شد - {len(page_contents)} {content_name} جدید اضافه شد")
                    else:
                        logger.info(f"⚠️ هیچ {content_name} جدیدی در صفحه {page_num} پیدا نشد")

                logger.info("🎉 استخراج کامل شد!")
                logger.info(f"📊 مجموع {content_name}‌های استخراج شده: {self.data['information'][total_key]}")
                logger.info(f"📝 مجموع بخش‌های استخراج شده: {self.data['information']['total_section_containers']}")

            finally:
                await browser.close()

async def main():
    # بررسی آرگومان‌های خط فرمان
    content_type = "speech"  # پیش‌فرض

    if len(sys.argv) > 1:
        if sys.argv[1] == "wisdom":
            content_type = "wisdom"
        elif sys.argv[1] == "speech":
            content_type = "speech"
        elif sys.argv[1] == "letter":
            content_type = "letter"
        else:
            print("استفاده: python nahj_scraper.py [speech|wisdom|letter]")
            print("speech: برای استخراج خطبه‌ها (پیش‌فرض)")
            print("wisdom: برای استخراج حکمت‌ها")
            print("letter: برای استخراج نامه‌ها")
            return

    scraper = NahjScraper(content_type)
    await scraper.run()

if __name__ == "__main__":
    asyncio.run(main())
