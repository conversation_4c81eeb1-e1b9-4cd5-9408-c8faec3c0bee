#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت بررسی و رفع خطبه‌های خالی
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def find_empty_speeches():
    """پیدا کردن خطبه‌های خالی"""
    try:
        with open('nahj_speeches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        empty_speeches = []
        for speech in data['speechs']:
            if speech['total_section_phrase_container'] == 0:
                empty_speeches.append(speech)
        
        return empty_speeches
    except Exception as e:
        logger.error(f"خطا در خواندن فایل: {e}")
        return []

async def fix_speech_content(page, speech_info):
    """رفع محتوای یک خطبه خالی"""
    logger.info(f"در حال رفع خطبه {speech_info['number']}: {speech_info['title']}")
    
    try:
        await page.goto(speech_info['url'], wait_until='networkidle')
        await page.wait_for_timeout(3000)
        
        containers = []
        
        # روش پیشرفته: جستجوی مستقیم برای h4 ها
        h4_elements = await page.query_selector_all('h4')
        logger.info(f"تعداد h4 های یافت شده: {len(h4_elements)}")
        
        for i, h4_element in enumerate(h4_elements, 1):
            try:
                header = await h4_element.inner_text()
                
                # استفاده از JavaScript برای پیدا کردن محتوا
                result = await page.evaluate('''(h4) => {
                    const result = {arabic: '', translate: ''};
                    
                    // پیدا کردن article والد
                    let article = h4.closest('article');
                    if (!article) {
                        article = h4.parentElement;
                        while (article && article.tagName !== 'ARTICLE') {
                            article = article.parentElement;
                        }
                    }
                    
                    if (article) {
                        // جستجو برای متن عربی
                        const arabicEl = article.querySelector('.arabic-text');
                        if (arabicEl) {
                            result.arabic = arabicEl.textContent.trim();
                        }
                        
                        // جستجو برای متن ترجمه
                        const translateEl = article.querySelector('.translate-text');
                        if (translateEl) {
                            result.translate = translateEl.textContent.trim();
                        }
                        
                        // اگر پیدا نشد، در p های بعدی جستجو کن
                        if (!result.arabic || !result.translate) {
                            let current = h4.nextElementSibling;
                            while (current) {
                                if (current.tagName === 'P') {
                                    const text = current.textContent.trim();
                                    // تشخیص متن عربی
                                    if (!result.arabic && /[\u0600-\u06FF]/.test(text) && text.length > 20) {
                                        result.arabic = text;
                                    }
                                    // تشخیص متن فارسی
                                    else if (!result.translate && text.length > 20 && !/^[\u0600-\u06FF\s]+$/.test(text)) {
                                        result.translate = text;
                                    }
                                }
                                current = current.nextElementSibling;
                                if (result.arabic && result.translate) break;
                            }
                        }
                    }
                    
                    return result;
                }''', h4_element)
                
                arabic_text = result.get('arabic', '')
                translate_text = result.get('translate', '')
                
                if header and (arabic_text or translate_text):
                    container_data = {
                        "number": i,
                        "header": header.strip(),
                        "arabic-text": arabic_text.strip(),
                        "translate-text": translate_text.strip()
                    }
                    containers.append(container_data)
                    logger.info(f"✅ بخش {i} استخراج شد: {header}")
                    
            except Exception as e:
                logger.error(f"خطا در استخراج بخش {i}: {e}")
                continue
        
        return containers
        
    except Exception as e:
        logger.error(f"خطا در رفع خطبه {speech_info['number']}: {e}")
        return []

async def update_speech_in_json(speech_number, containers):
    """به‌روزرسانی یک خطبه در فایل JSON"""
    try:
        with open('nahj_speeches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # پیدا کردن خطبه و به‌روزرسانی آن
        for speech in data['speechs']:
            if speech['number'] == speech_number:
                speech['containers'] = containers
                speech['total_section_phrase_container'] = len(containers)
                logger.info(f"✅ خطبه {speech_number} با {len(containers)} بخش به‌روزرسانی شد")
                break
        
        # به‌روزرسانی آمار کل
        total_containers = sum(speech['total_section_phrase_container'] for speech in data['speechs'])
        data['information']['total_section_containers'] = total_containers
        
        # ذخیره فایل
        with open('nahj_speeches.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        return True
        
    except Exception as e:
        logger.error(f"خطا در به‌روزرسانی فایل JSON: {e}")
        return False

async def main():
    """اجرای اصلی"""
    logger.info("🔍 جستجو برای خطبه‌های خالی...")
    
    empty_speeches = await find_empty_speeches()
    
    if not empty_speeches:
        logger.info("✅ هیچ خطبه خالی پیدا نشد!")
        return
    
    logger.info(f"📋 {len(empty_speeches)} خطبه خالی پیدا شد:")
    for speech in empty_speeches:
        print(f"  - خطبه {speech['number']}: {speech['title']}")
    
    # رفع خطبه‌های خالی
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            fixed_count = 0
            for speech in empty_speeches:
                logger.info(f"🔧 در حال رفع خطبه {speech['number']}...")
                
                containers = await fix_speech_content(page, speech)
                
                if containers:
                    success = await update_speech_in_json(speech['number'], containers)
                    if success:
                        fixed_count += 1
                        logger.info(f"✅ خطبه {speech['number']} با موفقیت رفع شد")
                    else:
                        logger.error(f"❌ خطا در ذخیره خطبه {speech['number']}")
                else:
                    logger.warning(f"⚠️ نتوانستم محتوای خطبه {speech['number']} را پیدا کنم")
                
                # تاخیر بین درخواست‌ها
                await asyncio.sleep(2)
            
            logger.info(f"🎉 {fixed_count} خطبه از {len(empty_speeches)} خطبه با موفقیت رفع شد")
            
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(main())
