#!/usr/bin/env python3
"""
دیباگ مشکل خطبه 723 - کانتینر اول
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
import re

# تنظیم لاگ
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_speech_723():
    """دیباگ مشکل خطبه 723"""
    
    url = "http://nahj.makarem.ir/speech/723"
    logger.info(f"شروع دیباگ خطبه 723: {url}")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            print("="*80)
            print("مرحله 1: بررسی کانتینرهای phrase-text-container")
            print("="*80)
            
            # بررسی تمام کانتینرهای phrase-text-container
            section_containers = await page.query_selector_all('.container-fluid article')
            print(f"تعداد کانتینرهای article: {len(section_containers)}")
            
            for i, container in enumerate(section_containers, 1):
                print(f"\n--- کانتینر {i} ---")
                
                # بررسی header
                header_element = await container.query_selector('h4')
                header = await header_element.inner_text() if header_element else ""
                print(f"Header: '{header}'")
                
                # بررسی phrase-text-container
                phrase_container = await container.query_selector('.phrase-text-container')
                if phrase_container:
                    print("✅ phrase-text-container یافت شد")
                    
                    # بررسی HTML کامل
                    container_html = await phrase_container.inner_html()
                    print(f"HTML length: {len(container_html)} کاراکتر")
                    print(f"HTML sample: {container_html[:200]}...")
                    
                    # بررسی عناصر عربی
                    arabic_elements = await phrase_container.query_selector_all('.arabic-text p, .arabic-text')
                    print(f"تعداد عناصر عربی: {len(arabic_elements)}")
                    
                    for j, element in enumerate(arabic_elements):
                        text = await element.inner_text()
                        print(f"  عربی {j+1}: {text[:50]}...")
                    
                    # بررسی عناصر ترجمه
                    translate_elements = await phrase_container.query_selector_all('.translate-text p, .translate-text')
                    print(f"تعداد عناصر ترجمه: {len(translate_elements)}")
                    
                    for j, element in enumerate(translate_elements):
                        text = await element.inner_text()
                        print(f"  ترجمه {j+1}: {text[:50]}...")
                    
                    # بررسی تمام عناصر p
                    all_p_elements = await phrase_container.query_selector_all('p')
                    print(f"تعداد کل عناصر p: {len(all_p_elements)}")
                    
                    for j, element in enumerate(all_p_elements):
                        class_name = await element.get_attribute('class')
                        text = await element.inner_text()
                        if text.strip():
                            print(f"  p {j+1}: class='{class_name}', text='{text[:30]}...'")
                else:
                    print("❌ phrase-text-container یافت نشد")
            
            print("\n" + "="*80)
            print("مرحله 2: تست استخراج با کد فعلی")
            print("="*80)
            
            # شبیه‌سازی کد فعلی
            containers = []
            
            for i, container in enumerate(section_containers, 1):
                try:
                    # استخراج header
                    header_element = await container.query_selector('h4')
                    header = await header_element.inner_text() if header_element else ""
                    
                    # استخراج تمام پاراگراف‌های متن عربی
                    arabic_elements = await container.query_selector_all('.arabic-text p, .arabic-text')
                    arabic_paragraphs = []
                    
                    for element in arabic_elements:
                        text = await element.inner_text()
                        if text.strip():
                            arabic_paragraphs.append(text.strip())
                    
                    # استخراج تمام پاراگراف‌های ترجمه
                    translate_elements = await container.query_selector_all('.translate-text p, .translate-text')
                    translate_paragraphs = []
                    
                    for element in translate_elements:
                        text = await element.inner_text()
                        if text.strip():
                            translate_paragraphs.append(text.strip())
                    
                    print(f"\nکانتینر {i}:")
                    print(f"  Header: '{header}'")
                    print(f"  عربی: {len(arabic_paragraphs)} پاراگراف")
                    print(f"  ترجمه: {len(translate_paragraphs)} پاراگراف")
                    
                    if arabic_paragraphs:
                        print(f"  عربی اول: {arabic_paragraphs[0][:50]}...")
                    if translate_paragraphs:
                        print(f"  ترجمه اول: {translate_paragraphs[0][:50]}...")
                    
                    # ساخت container_data
                    if arabic_paragraphs or translate_paragraphs:
                        arabic_data = {
                            "main": arabic_paragraphs[0] if arabic_paragraphs else "",
                            "metadata": arabic_paragraphs[1:] if len(arabic_paragraphs) > 1 else None
                        }
                        
                        translate_data = {
                            "main": translate_paragraphs[0] if translate_paragraphs else "",
                            "metadata": translate_paragraphs[1:] if len(translate_paragraphs) > 1 else None
                        }
                        
                        container_data = {
                            "number": i,
                            "header": header,
                            "arabic-text": arabic_data,
                            "translate-text": translate_data
                        }
                        
                        containers.append(container_data)
                        
                        print(f"  ✅ کانتینر {i} ساخته شد")
                        print(f"     عربی main: {len(arabic_data['main'])} کاراکتر")
                        print(f"     ترجمه main: {len(translate_data['main'])} کاراکتر")
                    else:
                        print(f"  ❌ کانتینر {i} خالی")
                        
                except Exception as e:
                    print(f"  ❌ خطا در کانتینر {i}: {e}")
            
            print(f"\nتعداد کل کانتینرهای استخراج شده: {len(containers)}")
            
            # بررسی کانتینر اول
            if containers:
                first_container = containers[0]
                print(f"\nکانتینر اول:")
                print(f"  Header: '{first_container['header']}'")
                print(f"  عربی: '{first_container['arabic-text']['main']}'")
                print(f"  ترجمه: '{first_container['translate-text']['main']}'")
                
                if not first_container['translate-text']['main']:
                    print("🔍 مشکل تأیید شد: کانتینر اول ترجمه ندارد!")
                else:
                    print("✅ کانتینر اول کامل است")
            
        except Exception as e:
            logger.error(f"خطا در دیباگ: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_speech_723())
