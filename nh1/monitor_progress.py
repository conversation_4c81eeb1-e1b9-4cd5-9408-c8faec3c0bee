#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت نمایش پیشرفت لحظه‌ای استخراج خطبه‌ها
"""

import json
import os
import time
import sys

def monitor_progress():
    output_file = "nahj_speeches.json"
    estimated_total = 193  # بر اساس لاگ اسکریپت
    
    print("🔄 نمایش پیشرفت لحظه‌ای استخراج خطبه‌ها")
    print("برای توقف از Ctrl+C استفاده کنید\n")
    
    last_count = 0
    
    try:
        while True:
            if os.path.exists(output_file):
                try:
                    with open(output_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    current_count = data['information']['total_speech']
                    total_containers = data['information']['total_section_containers']
                    
                    # محاسبه درصد پیشرفت
                    progress_percent = (current_count / estimated_total) * 100
                    
                    # نمایش نوار پیشرفت
                    bar_length = 30
                    filled_length = int(bar_length * current_count // estimated_total)
                    bar = '█' * filled_length + '-' * (bar_length - filled_length)
                    
                    # پاک کردن خط قبلی و نمایش جدید
                    sys.stdout.write('\r')
                    sys.stdout.write(f'📊 پیشرفت: |{bar}| {progress_percent:.1f}% ({current_count}/{estimated_total}) - بخش‌ها: {total_containers}')
                    sys.stdout.flush()
                    
                    # اگر تعداد تغییر کرده، خط جدید اضافه کن
                    if current_count != last_count:
                        if current_count > 0:
                            latest_speech = data['speechs'][-1]
                            print(f"\n✅ آخرین خطبه: {latest_speech['number']} - {latest_speech['title'][:50]}...")
                        last_count = current_count
                    
                    # بررسی تکمیل
                    if current_count >= estimated_total:
                        print(f"\n🎉 استخراج کامل شد! مجموع {current_count} خطبه با {total_containers} بخش")
                        break
                        
                except json.JSONDecodeError:
                    sys.stdout.write('\r⚠️  در حال بارگذاری فایل JSON...')
                    sys.stdout.flush()
                except Exception as e:
                    sys.stdout.write(f'\r❌ خطا: {e}')
                    sys.stdout.flush()
            else:
                sys.stdout.write('\r⏳ در انتظار ایجاد فایل JSON...')
                sys.stdout.flush()
            
            time.sleep(2)  # بررسی هر 2 ثانیه
            
    except KeyboardInterrupt:
        print(f"\n\n⏹️  نمایش پیشرفت متوقف شد.")
        if os.path.exists(output_file):
            with open(output_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"📊 وضعیت نهایی: {data['information']['total_speech']} خطبه استخراج شده")

if __name__ == "__main__":
    monitor_progress()
