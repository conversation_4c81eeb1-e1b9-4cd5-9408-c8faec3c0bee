#!/usr/bin/env python3
"""
پیدا کردن حکمت‌های خالی
"""

import json

def find_empty_wisdoms():
    """پیدا کردن حکمت‌های خالی"""
    
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"خطا در بارگذاری فایل: {e}")
        return
    
    empty_wisdoms = []
    
    for wisdom in data['wisdoms']:
        # بررسی حکمت‌هایی که کانتینر ندارند یا کانتینرشان خالی است
        if not wisdom['containers'] or wisdom['total_section_phrase_container'] == 0:
            empty_wisdoms.append({
                'number': wisdom['number'],
                'title': wisdom['title'],
                'url': wisdom['url'],
                'containers_count': len(wisdom['containers']),
                'total_section': wisdom['total_section_phrase_container']
            })
    
    print(f"تعداد حکمت‌های خالی: {len(empty_wisdoms)}")
    print("="*80)
    
    if empty_wisdoms:
        print("حکمت‌های خالی:")
        print("-" * 80)
        for wisdom in empty_wisdoms[:20]:  # نمایش 20 تای اول
            print(f"شماره: {wisdom['number']}, عنوان: {wisdom['title'][:50]}...")
            print(f"  کانتینرها: {wisdom['containers_count']}, total_section: {wisdom['total_section']}")
            print(f"  URL: {wisdom['url']}")
            print()
        
        if len(empty_wisdoms) > 20:
            print(f"... و {len(empty_wisdoms) - 20} حکمت دیگر")
    else:
        print("هیچ حکمت خالی یافت نشد! ✅")

if __name__ == "__main__":
    find_empty_wisdoms()
