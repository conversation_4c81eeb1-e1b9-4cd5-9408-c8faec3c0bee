# 📖 استخراج خطبه‌های نهج البلاغه

این پروژه برای استخراج کامل خطبه‌های نهج البلاغه از سایت [nahj.makarem.ir](http://nahj.makarem.ir) طراحی شده است.

## ✨ ویژگی‌ها

- 🔄 **استخراج خودکار**: استخراج تمام خطبه‌ها از 11 صفحه سایت
- 📝 **استخراج کامل محتوا**: شامل عنوان، متن عربی، ترجمه فارسی و بخش‌بندی
- 💾 **ذخیره‌سازی تدریجی**: ذخیره داده‌ها پس از هر خطبه برای جلوگیری از از دست رفتن اطلاعات
- 🔁 **قابلیت ادامه**: امکان ادامه فرآیند از جایی که متوقف شده
- 📊 **نمایش پیشرفت**: ابزارهای نمایش پیشرفت لحظه‌ای
- 🌐 **هدلس**: اجرا بدون نمایش مرورگر برای سرعت بیشتر

## 🛠️ نصب و راه‌اندازی

### پیش‌نیازها

```bash
pip install playwright
playwright install
```

### اجرای اسکریپت

```bash
python nahj_scraper.py
```

### نمایش پیشرفت

```bash
# بررسی وضعیت فعلی
python check_progress.py

# نمایش پیشرفت لحظه‌ای
python monitor_progress.py
```

## 📁 ساختار فایل‌ها

```
nh1/
├── nahj_scraper.py          # اسکریپت اصلی استخراج
├── check_progress.py        # بررسی پیشرفت
├── monitor_progress.py      # نمایش پیشرفت لحظه‌ای
├── nahj_speeches.json       # فایل خروجی JSON
├── nahj_scraper.log         # فایل لاگ
└── README.md               # این فایل
```

## 📋 ساختار داده‌های خروجی

فایل `nahj_speeches.json` با ساختار زیر تولید می‌شود:

```json
{
  "information": {
    "total_speech": 193,
    "total_pages": 11,
    "total_section_containers": 1250
  },
  "speechs": [
    {
      "number": 1,
      "page": 1,
      "title": "خطبه 1 - آغاز آفرينش آسمان و زمين آدم",
      "total_section_phrase_container": 16,
      "containers": [
        {
          "number": 1,
          "header": "بخش اوّل",
          "arabic-text": "اَلْحَمْدُ للهِ الَّذِي لا يَبْلُغُ...",
          "translate-text": "ستايش مخصوص خداوندى است که..."
        }
      ]
    }
  ]
}
```

## 🔧 تنظیمات

### تغییر تاخیر بین درخواست‌ها

در فایل `nahj_scraper.py` می‌توانید تاخیر بین درخواست‌ها را تغییر دهید:

```python
await asyncio.sleep(2)  # تاخیر 2 ثانیه بین خطبه‌ها
```

### تغییر حالت مرورگر

برای نمایش مرورگر در حین اجرا:

```python
browser = await p.chromium.launch(headless=False)  # تغییر به False
```

## 📊 آمار استخراج

- **تعداد کل خطبه‌ها**: 193 خطبه
- **تعداد صفحات**: 11 صفحه
- **زمان تخمینی**: حدود 10-15 دقیقه
- **حجم فایل خروجی**: حدود 2-3 مگابایت

## 🚨 نکات مهم

1. **احترام به سرور**: اسکریپت تاخیر مناسب بین درخواست‌ها دارد
2. **ذخیره‌سازی ایمن**: داده‌ها پس از هر خطبه ذخیره می‌شوند
3. **مدیریت خطا**: خطاها لاگ شده و فرآیند ادامه می‌یابد
4. **قابلیت ادامه**: در صورت قطع، از جایی که متوقف شده ادامه می‌یابد

## 📝 لاگ‌ها

تمام فعالیت‌ها در فایل `nahj_scraper.log` ثبت می‌شوند:

```bash
tail -f nahj_scraper.log  # نمایش لاگ‌های زنده
```

## 🤝 مشارکت

برای بهبود این پروژه:

1. مسائل را در Issues گزارش دهید
2. پیشنهادات خود را ارسال کنید
3. Pull Request ارسال کنید

## 📄 مجوز

این پروژه تحت مجوز MIT منتشر شده است.

## 🙏 تشکر

از سایت [nahj.makarem.ir](http://nahj.makarem.ir) برای ارائه این منبع ارزشمند تشکر می‌کنیم.
