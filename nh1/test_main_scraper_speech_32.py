#!/usr/bin/env python3
"""
تست اسکریپت اصلی برای خطبه 32
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_main_scraper_speech_32():
    """تست اسکریپت اصلی برای خطبه 32"""
    
    speech_info = {
        "number": 32,
        "url": "http://nahj.makarem.ir/speech/32",
        "title": "خطبه 5 - پس از رحلت رسول خدا"
    }
    
    scraper = NahjScraper("speech")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info(f"🧪 تست اسکریپت اصلی برای خطبه {speech_info['number']}")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, speech_info)
            
            print("\n" + "="*80)
            print(f"نتایج استخراج خطبه {speech_info['number']} با اسکریپت اصلی:")
            print("="*80)
            
            if result and 'containers' in result and result['containers']:
                print(f"تعداد کانتینرها: {len(result['containers'])}")
                
                # بررسی کانتینر اول
                first_container = result['containers'][0]
                arabic_text = first_container.get('arabic-text', {}).get('main', '')
                translate_text = first_container.get('translate-text', {}).get('main', '')
                header = first_container.get('header', '')
                
                print(f"\n🔍 کانتینر اول:")
                print(f"  Header: '{header}'")
                print(f"  عربی: {len(arabic_text)} کاراکتر")
                print(f"  ترجمه: {len(translate_text)} کاراکتر")
                print(f"  عربی: '{arabic_text[:50]}...'")
                print(f"  ترجمه: '{translate_text[:50]}...'")
                
                # تست‌های اساسی
                success = True
                
                if not arabic_text:
                    print("❌ متن عربی خالی است")
                    success = False
                else:
                    print("✅ متن عربی صحیح است")
                
                if not translate_text:
                    print("❌ متن ترجمه خالی است")
                    success = False
                elif 'امام' not in translate_text:
                    print("❌ متن ترجمه شامل 'امام' نیست")
                    success = False
                else:
                    print("✅ متن ترجمه صحیح است")
                
                if success:
                    print("\n🎉 تست کامل موفق! اسکریپت اصلی به درستی کار می‌کند.")
                    
                    # مقایسه با فایل JSON فعلی
                    try:
                        with open('nahj_speeches.json', 'r', encoding='utf-8') as f:
                            current_data = json.load(f)
                        
                        # پیدا کردن خطبه 32
                        current_speech = None
                        for speech in current_data['speechs']:
                            if speech['number'] == 32:
                                current_speech = speech
                                break
                        
                        if current_speech:
                            current_first = current_speech['containers'][0]
                            current_translate = current_first.get('translate-text', {}).get('main', '')
                            
                            print(f"\n📊 مقایسه با فایل فعلی:")
                            print(f"  فایل فعلی - ترجمه: {len(current_translate)} کاراکتر")
                            print(f"  اسکریپت جدید - ترجمه: {len(translate_text)} کاراکتر")
                            
                            if len(current_translate) < len(translate_text):
                                print("🔧 اسکریپت جدید بهتر است! نیاز به به‌روزرسانی فایل JSON")
                                return True
                            else:
                                print("ℹ️ فایل فعلی به‌روز است")
                                return True
                    except Exception as e:
                        print(f"⚠️ خطا در مقایسه با فایل فعلی: {e}")
                        return True
                else:
                    print("\n❌ تست ناموفق")
                    return False
            else:
                print("❌ هیچ کانتینری یافت نشد یا نتیجه خالی است")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطا در تست: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
            return False
        
        finally:
            await browser.close()

if __name__ == "__main__":
    result = asyncio.run(test_main_scraper_speech_32())
    if result:
        print("\n🎯 اسکریپت اصلی آماده است!")
    else:
        print("\n⚠️ نیاز به بررسی بیشتر دارد.")
