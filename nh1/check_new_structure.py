#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بررسی ساختار جدید فایل JSON
"""

import json
import os

def check_new_structure():
    """بررسی ساختار جدید"""
    output_file = "nahj_speeches.json"
    
    if not os.path.exists(output_file):
        print("❌ فایل JSON وجود ندارد.")
        return
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_speeches = data['information']['total_speech']
        total_containers = data['information']['total_section_containers']
        
        print(f"📊 گزارش ساختار جدید:")
        print(f"   📖 تعداد خطبه‌های استخراج شده: {total_speeches}")
        print(f"   📝 تعداد کل بخش‌ها: {total_containers}")
        
        # آمار ساختار جدید
        single_paragraph_count = 0
        multi_paragraph_count = 0
        empty_count = 0
        
        for speech in data['speechs']:
            for container in speech['containers']:
                # بررسی arabic-text
                arabic = container['arabic-text']
                if arabic['main'] == "":
                    empty_count += 1
                elif arabic['metadata'] is None:
                    single_paragraph_count += 1
                else:
                    multi_paragraph_count += 1
        
        print(f"\n📈 آمار ساختار:")
        print(f"   📝 بخش‌های تک پاراگراف: {single_paragraph_count}")
        print(f"   📚 بخش‌های چند پاراگراف: {multi_paragraph_count}")
        print(f"   ⚪ بخش‌های خالی: {empty_count}")
        
        # نمایش نمونه‌ها
        print(f"\n🔍 نمونه ساختار جدید:")
        
        sample_count = 0
        for speech in data['speechs']:
            if sample_count >= 3:
                break
            
            for container in speech['containers']:
                if sample_count >= 3:
                    break
                
                print(f"\n--- خطبه {speech['number']}, بخش {container['number']}: {container['header']} ---")
                
                # نمایش arabic-text
                arabic = container['arabic-text']
                print(f"🔤 Arabic-text:")
                print(f"   📝 Main: {len(arabic['main'])} کاراکتر")
                if arabic['metadata']:
                    print(f"   📋 Metadata: {len(arabic['metadata'])} پاراگراف")
                else:
                    print(f"   📋 Metadata: None")
                
                # نمایش translate-text
                translate = container['translate-text']
                print(f"🔤 Translate-text:")
                print(f"   📝 Main: {len(translate['main'])} کاراکتر")
                if translate['metadata']:
                    print(f"   📋 Metadata: {len(translate['metadata'])} پاراگراف")
                else:
                    print(f"   📋 Metadata: None")
                
                sample_count += 1
        
        # بررسی صحت ساختار
        print(f"\n✅ بررسی صحت ساختار:")
        structure_valid = True
        
        for speech in data['speechs']:
            for container in speech['containers']:
                # بررسی arabic-text
                arabic = container['arabic-text']
                if not isinstance(arabic, dict) or 'main' not in arabic or 'metadata' not in arabic:
                    print(f"❌ ساختار نادرست در خطبه {speech['number']}, بخش {container['number']}")
                    structure_valid = False
                
                # بررسی translate-text
                translate = container['translate-text']
                if not isinstance(translate, dict) or 'main' not in translate or 'metadata' not in translate:
                    print(f"❌ ساختار نادرست در خطبه {speech['number']}, بخش {container['number']}")
                    structure_valid = False
        
        if structure_valid:
            print("✅ تمام بخش‌ها دارای ساختار صحیح هستند")
        
        # نمایش JSON نمونه
        if data['speechs']:
            sample_container = data['speechs'][0]['containers'][0] if data['speechs'][0]['containers'] else None
            if sample_container:
                print(f"\n📄 نمونه ساختار JSON:")
                sample_json = {
                    "number": sample_container['number'],
                    "header": sample_container['header'],
                    "arabic-text": sample_container['arabic-text'],
                    "translate-text": sample_container['translate-text']
                }
                print(json.dumps(sample_json, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"❌ خطا در خواندن فایل: {e}")

if __name__ == "__main__":
    check_new_structure()
