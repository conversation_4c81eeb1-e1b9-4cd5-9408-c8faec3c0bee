#!/usr/bin/env python3
"""
رفع مستقیم حکمت 1048
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_wisdom_1048_directly():
    """رفع مستقیم حکمت 1048"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت 1048
    wisdom_1048 = None
    wisdom_index = -1
    for i, wisdom in enumerate(data['wisdoms']):
        if wisdom['number'] == 1048:
            wisdom_1048 = wisdom
            wisdom_index = i
            break
    
    if not wisdom_1048:
        logger.error("حکمت 1048 یافت نشد!")
        return
    
    logger.info(f"حکمت 1048 یافت شد: {wisdom_1048['title']}")
    logger.info(f"URL: {wisdom_1048['url']}")
    logger.info(f"وضعیت فعلی: {len(wisdom_1048['containers'])} کانتینر")
    
    # استخراج محتوا با اسکریپت اصلی
    scraper = NahjScraper("wisdom")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info("در حال استخراج محتوا با اسکریپت اصلی...")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, wisdom_1048)
            
            if result and 'containers' in result and result['containers'] and len(result['containers']) > 0:
                new_containers = result['containers']
                new_total = result.get('total_section_phrase_container', len(new_containers))
                
                # به‌روزرسانی حکمت در داده
                data['wisdoms'][wisdom_index]['containers'] = new_containers
                data['wisdoms'][wisdom_index]['total_section_phrase_container'] = new_total
                
                # ذخیره فایل
                with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ حکمت 1048 با موفقیت رفع شد!")
                logger.info(f"تعداد کانتینرها: {len(new_containers)}")
                logger.info(f"total_section_phrase_container: {new_total}")
                
                # نمایش نمونه محتوا
                print("\n" + "="*60)
                print("محتوای رفع شده:")
                print("="*60)
                
                first_container = new_containers[0]
                arabic_text = first_container.get('arabic-text', {}).get('main', '')
                translate_text = first_container.get('translate-text', {}).get('main', '')
                
                print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
                print("-" * 40)
                print(arabic_text[:200] + "..." if len(arabic_text) > 200 else arabic_text)
                
                print(f"\nمتن ترجمه ({len(translate_text)} کاراکتر):")
                print("-" * 40)
                print(translate_text[:200] + "..." if len(translate_text) > 200 else translate_text)
                
            else:
                logger.error("❌ اسکریپت اصلی هیچ کانتینری استخراج نکرد")
                logger.error(f"نتیجه: {result}")
                
        except Exception as e:
            logger.error(f"خطا در استخراج: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(fix_wisdom_1048_directly())
