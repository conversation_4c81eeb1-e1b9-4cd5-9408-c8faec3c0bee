#!/usr/bin/env python3
"""
رفع قوی حکمت 1189 با روش‌های مختلف
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
import re

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_wisdom_1189_robust():
    """رفع قوی حکمت 1189 با روش‌های مختلف"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت 1189
    wisdom_1189 = None
    wisdom_index = -1
    for i, wisdom in enumerate(data['wisdoms']):
        if wisdom['number'] == 1189:
            wisdom_1189 = wisdom
            wisdom_index = i
            break
    
    if not wisdom_1189:
        logger.error("حکمت 1189 یافت نشد!")
        return
    
    logger.info(f"حکمت 1189 یافت شد: {wisdom_1189['title']}")
    logger.info(f"URL: {wisdom_1189['url']}")
    
    # استخراج محتوا
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # نمایش مرورگر برای دیباگ
        page = await browser.new_page()
        
        try:
            logger.info("در حال بارگذاری صفحه...")
            await page.goto(wisdom_1189['url'], wait_until='networkidle')
            await page.wait_for_timeout(3000)  # انتظار بیشتر
            
            arabic_text = ""
            translate_text = ""
            
            # روش 1: جستجو برای div اصلی
            logger.info("روش 1: جستجو برای div اصلی")
            main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
            
            if main_content_div:
                logger.info("✅ div اصلی یافت شد")
                
                # استخراج متن عربی
                arabic_element = await main_content_div.query_selector('p.arabic-text')
                if arabic_element:
                    arabic_html = await arabic_element.inner_html()
                    arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                    arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                    logger.info(f"✅ متن عربی استخراج شد: {len(arabic_text)} کاراکتر")
                
                # استخراج متن ترجمه
                translate_element = await main_content_div.query_selector('p.translate-text')
                if translate_element:
                    translate_html = await translate_element.inner_html()
                    translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                    translate_text = re.sub(r'<[^>]+>', '', translate_text)
                    translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                    logger.info(f"✅ متن ترجمه استخراج شد: {len(translate_text)} کاراکتر")
            else:
                logger.warning("❌ div اصلی یافت نشد")
            
            # روش 2: جستجو مستقیم برای عناصر
            if not arabic_text or not translate_text:
                logger.info("روش 2: جستجو مستقیم برای عناصر")
                
                if not arabic_text:
                    arabic_elements = await page.query_selector_all('p.arabic-text')
                    logger.info(f"تعداد عناصر عربی یافت شده: {len(arabic_elements)}")
                    
                    if arabic_elements:
                        arabic_texts = []
                        for element in arabic_elements:
                            text = await element.inner_text()
                            if text.strip():
                                arabic_texts.append(text.strip())
                        arabic_text = '\n'.join(arabic_texts)
                        logger.info(f"✅ متن عربی از روش 2: {len(arabic_text)} کاراکتر")
                
                if not translate_text:
                    translate_elements = await page.query_selector_all('p.translate-text')
                    logger.info(f"تعداد عناصر ترجمه یافت شده: {len(translate_elements)}")
                    
                    if translate_elements:
                        translate_texts = []
                        for element in translate_elements:
                            text = await element.inner_text()
                            if text.strip():
                                translate_texts.append(text.strip())
                        translate_text = '\n'.join(translate_texts)
                        logger.info(f"✅ متن ترجمه از روش 2: {len(translate_text)} کاراکتر")
            
            # روش 3: جستجو با CSS selector های مختلف
            if not arabic_text or not translate_text:
                logger.info("روش 3: جستجو با CSS selector های مختلف")
                
                # تست selector های مختلف
                selectors_to_try = [
                    '.arabic-text',
                    'p[class*="arabic"]',
                    'p[style*="20pt"]',
                    '.card-text.arabic-text'
                ]
                
                for selector in selectors_to_try:
                    if not arabic_text:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            logger.info(f"یافت شد با selector: {selector} ({len(elements)} عنصر)")
                            for element in elements:
                                text = await element.inner_text()
                                if text.strip() and any('\u0600' <= char <= '\u06FF' for char in text):
                                    arabic_text = text.strip()
                                    logger.info(f"✅ متن عربی از selector {selector}: {len(arabic_text)} کاراکتر")
                                    break
                
                translate_selectors = [
                    '.translate-text',
                    'p[class*="translate"]',
                    'p[style*="14pt"]',
                    '.card-text.translate-text'
                ]
                
                for selector in translate_selectors:
                    if not translate_text:
                        elements = await page.query_selector_all(selector)
                        if elements:
                            logger.info(f"یافت شد با selector: {selector} ({len(elements)} عنصر)")
                            for element in elements:
                                text = await element.inner_text()
                                if text.strip() and 'امام' in text:
                                    translate_text = text.strip()
                                    logger.info(f"✅ متن ترجمه از selector {selector}: {len(translate_text)} کاراکتر")
                                    break
            
            # بررسی نهایی و ذخیره
            if arabic_text or translate_text:
                # به‌روزرسانی داده
                container_data = {
                    "number": 1,
                    "header": "",
                    "arabic-text": {"main": arabic_text, "metadata": None},
                    "translate-text": {"main": translate_text, "metadata": None}
                }
                
                # به‌روزرسانی حکمت در داده
                data['wisdoms'][wisdom_index]['containers'] = [container_data]
                data['wisdoms'][wisdom_index]['total_section_phrase_container'] = 1
                
                # ذخیره فایل
                with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ حکمت 1189 با موفقیت رفع شد!")
                logger.info(f"متن عربی: {len(arabic_text)} کاراکتر")
                logger.info(f"متن ترجمه: {len(translate_text)} کاراکتر")
                
                # نمایش محتوا
                print("\n" + "="*60)
                print("محتوای استخراج شده:")
                print("="*60)
                
                if arabic_text:
                    print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
                    print("-" * 40)
                    print(arabic_text)
                
                if translate_text:
                    print(f"\nمتن ترجمه ({len(translate_text)} کاراکتر):")
                    print("-" * 40)
                    print(translate_text)
                
            else:
                logger.error("❌ نتوانستیم محتوای حکمت 1189 را با هیچ روشی استخراج کنیم")
                
                # نمایش محتوای کامل صفحه برای دیباگ
                page_content = await page.content()
                print("محتوای کامل صفحه:")
                print(page_content[:1000] + "...")
                
        except Exception as e:
            logger.error(f"خطا در استخراج: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(fix_wisdom_1189_robust())
