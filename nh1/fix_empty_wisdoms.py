#!/usr/bin/env python3
"""
اسکریپت برای پیدا کردن و رفع حکمت‌های خالی
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
import re

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def extract_wisdom_content(page, url):
    """استخراج محتوای یک حکمت"""
    try:
        await page.goto(url, wait_until='domcontentloaded')
        await page.wait_for_timeout(1000)
        
        arabic_text = ""
        translate_text = ""
        
        # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
        main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
        
        if main_content_div:
            # استخراج متن عربی از p با کلاس arabic-text
            arabic_element = await main_content_div.query_selector('p.arabic-text')
            if arabic_element:
                # استخراج HTML برای حفظ ساختار
                arabic_html = await arabic_element.inner_html()
                # تمیز کردن HTML و تبدیل <br> به \n
                arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                # حذف سایر تگ‌های HTML
                arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                # تمیز کردن فضاهای اضافی
                arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                
            # استخراج متن ترجمه از p با کلاس translate-text
            translate_element = await main_content_div.query_selector('p.translate-text')
            if translate_element:
                # استخراج HTML برای حفظ ساختار
                translate_html = await translate_element.inner_html()
                # تمیز کردن HTML و تبدیل <br> به \n
                translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                # حذف لینک‌های پاورقی
                translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                # حذف سایر تگ‌های HTML
                translate_text = re.sub(r'<[^>]+>', '', translate_text)
                # تمیز کردن فضاهای اضافی
                translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())

        # اگر روش اول کار نکرد، از روش جایگزین استفاده کن
        if not arabic_text and not translate_text:
            # جستجو برای تمام p elements با کلاس‌های مربوطه
            arabic_elements = await page.query_selector_all('p.arabic-text')
            translate_elements = await page.query_selector_all('p.translate-text')
            
            if arabic_elements:
                arabic_texts = []
                for element in arabic_elements:
                    text = await element.inner_text()
                    if text.strip():
                        arabic_texts.append(text.strip())
                arabic_text = '\n'.join(arabic_texts)
                
            if translate_elements:
                translate_texts = []
                for element in translate_elements:
                    text = await element.inner_text()
                    if text.strip():
                        translate_texts.append(text.strip())
                translate_text = '\n'.join(translate_texts)

        return arabic_text, translate_text
        
    except Exception as e:
        logger.error(f"خطا در استخراج محتوا از {url}: {e}")
        return "", ""

async def fix_empty_wisdoms():
    """پیدا کردن و رفع حکمت‌های خالی"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت‌های خالی
    empty_wisdoms = []
    for wisdom in data['wisdoms']:
        if not wisdom['containers'] or len(wisdom['containers']) == 0:
            empty_wisdoms.append(wisdom)
        elif len(wisdom['containers']) == 1:
            container = wisdom['containers'][0]
            arabic_empty = not container.get('arabic-text', {}).get('main', '').strip()
            translate_empty = not container.get('translate-text', {}).get('main', '').strip()
            if arabic_empty and translate_empty:
                empty_wisdoms.append(wisdom)
    
    logger.info(f"تعداد حکمت‌های خالی یافت شده: {len(empty_wisdoms)}")
    
    if not empty_wisdoms:
        logger.info("هیچ حکمت خالی یافت نشد!")
        return
    
    # نمایش لیست حکمت‌های خالی
    print("\nحکمت‌های خالی:")
    print("-" * 50)
    for wisdom in empty_wisdoms[:10]:  # نمایش 10 تای اول
        print(f"شماره: {wisdom['number']}, عنوان: {wisdom['title']}")
    
    if len(empty_wisdoms) > 10:
        print(f"... و {len(empty_wisdoms) - 10} حکمت دیگر")
    
    # تأیید برای ادامه
    response = input(f"\nآیا می‌خواهید {len(empty_wisdoms)} حکمت خالی را دوباره استخراج کنید؟ (y/n): ")
    if response.lower() != 'y':
        return
    
    # استخراج مجدد حکمت‌های خالی
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        fixed_count = 0
        
        for i, wisdom in enumerate(empty_wisdoms, 1):
            logger.info(f"[{i}/{len(empty_wisdoms)}] در حال رفع حکمت {wisdom['number']}: {wisdom['title'][:50]}...")
            
            arabic_text, translate_text = await extract_wisdom_content(page, wisdom['url'])
            
            if arabic_text or translate_text:
                # به‌روزرسانی داده
                container_data = {
                    "number": 1,
                    "header": "",
                    "arabic-text": {"main": arabic_text, "metadata": None},
                    "translate-text": {"main": translate_text, "metadata": None}
                }
                wisdom['containers'] = [container_data]
                wisdom['total_section_phrase_container'] = 1
                
                fixed_count += 1
                logger.info(f"✅ حکمت {wisdom['number']} رفع شد - عربی: {len(arabic_text)} کاراکتر، ترجمه: {len(translate_text)} کاراکتر")
            else:
                logger.warning(f"❌ نتوانستیم محتوای حکمت {wisdom['number']} را استخراج کنیم")
            
            # ذخیره هر 5 حکمت
            if i % 5 == 0:
                with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                logger.info(f"💾 پیشرفت ذخیره شد ({i}/{len(empty_wisdoms)})")
        
        await browser.close()
    
    # ذخیره نهایی
    with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    logger.info(f"🎉 کار تمام شد! {fixed_count} حکمت از {len(empty_wisdoms)} حکمت خالی رفع شد")

if __name__ == "__main__":
    asyncio.run(fix_empty_wisdoms())
