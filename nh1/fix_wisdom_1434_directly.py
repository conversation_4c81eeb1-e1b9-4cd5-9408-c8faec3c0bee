#!/usr/bin/env python3
"""
رفع مستقیم حکمت 1434
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_wisdom_1434_directly():
    """رفع مستقیم حکمت 1434"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت 1434
    wisdom_1434 = None
    wisdom_index = -1
    for i, wisdom in enumerate(data['wisdoms']):
        if wisdom['number'] == 1434:
            wisdom_1434 = wisdom
            wisdom_index = i
            break
    
    if not wisdom_1434:
        logger.error("حکمت 1434 یافت نشد!")
        return
    
    logger.info(f"حکمت 1434 یافت شد: {wisdom_1434['title']}")
    logger.info(f"URL: {wisdom_1434['url']}")
    
    # بررسی وضعیت فعلی
    if wisdom_1434['containers']:
        first_container = wisdom_1434['containers'][0]
        current_translate = first_container.get('translate-text', {}).get('main', '')
        logger.info(f"وضعیت فعلی: ترجمه {len(current_translate)} کاراکتر")
        
        if current_translate:
            logger.info("حکمت قبلاً دارای ترجمه است. خروج...")
            return
    
    # استخراج محتوا با اسکریپت اصلی
    scraper = NahjScraper("wisdom")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info("در حال استخراج محتوا با اسکریپت بهبود یافته...")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, wisdom_1434)
            
            if result and 'containers' in result and result['containers'] and len(result['containers']) > 0:
                new_container = result['containers'][0]
                new_translate = new_container.get('translate-text', {}).get('main', '')
                
                if new_translate:
                    # به‌روزرسانی حکمت در داده
                    data['wisdoms'][wisdom_index]['containers'][0] = new_container
                    
                    # ذخیره فایل
                    with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"✅ حکمت 1434 با موفقیت رفع شد!")
                    logger.info(f"ترجمه: {len(new_translate)} کاراکتر")
                    
                    # نمایش نمونه محتوا
                    print("\n" + "="*60)
                    print("محتوای رفع شده:")
                    print("="*60)
                    
                    arabic_text = new_container.get('arabic-text', {}).get('main', '')
                    print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
                    print("-" * 40)
                    print(arabic_text)
                    
                    print(f"\nمتن ترجمه ({len(new_translate)} کاراکتر):")
                    print("-" * 40)
                    print(new_translate)
                    
                else:
                    logger.error("❌ اسکریپت جدید نتوانست ترجمه استخراج کند")
            else:
                logger.error("❌ اسکریپت جدید هیچ کانتینری استخراج نکرد")
                
        except Exception as e:
            logger.error(f"خطا در استخراج: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(fix_wisdom_1434_directly())
