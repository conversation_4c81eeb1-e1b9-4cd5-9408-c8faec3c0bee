#!/usr/bin/env python3
"""
TDD Test برای رفع مشکل حکمت 1434
"""

import asyncio
from playwright.async_api import async_playwright

class TestWisdom1434:
    """تست‌های TDD برای حکمت 1434"""
    
    async def test_wisdom_1434_should_have_translation(self):
        """تست: حکمت 1434 باید ترجمه داشته باشد"""
        
        # Arrange
        url = "http://nahj.makarem.ir/wisdom/1434"
        expected_arabic_contains = "قَالَ عَلَيهِ السَّلَام"
        expected_translation_contains = "امام (عليه السلام)"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
            
            try:
                # Act
                result = await self.extract_wisdom_current_method(page, url)
                
                # Assert
                assert result is not None, "حکمت نباید None باشد"
                assert result['arabic_text'], "متن عربی نباید خالی باشد"
                assert result['translate_text'], "متن ترجمه نباید خالی باشد"
                assert expected_translation_contains in result['translate_text'], f"ترجمه باید شامل '{expected_translation_contains}' باشد"
                
                print(f"✅ تست موفق:")
                print(f"   عربی: {len(result['arabic_text'])} کاراکتر")
                print(f"   ترجمه: {len(result['translate_text'])} کاراکتر")
                print(f"   ترجمه: {result['translate_text'][:100]}...")
                
            finally:
                await browser.close()
    
    async def extract_wisdom_current_method(self, page, url):
        """استخراج حکمت با روش فعلی"""
        
        await page.goto(url, wait_until='networkidle')
        await page.wait_for_timeout(2000)
        
        arabic_text = ""
        translate_text = ""
        
        # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
        main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
        
        if main_content_div:
            print("✅ div اصلی یافت شد")
            
            # استخراج متن عربی از p با کلاس card-text arabic-text
            arabic_element = await main_content_div.query_selector('p.card-text.arabic-text')
            if arabic_element:
                print("✅ element عربی یافت شد")
                # استخراج HTML برای حفظ ساختار
                arabic_html = await arabic_element.inner_html()
                print(f"HTML عربی: {arabic_html[:200]}...")
                
                # تمیز کردن HTML و تبدیل <br> به \n
                arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                # حذف سایر تگ‌های HTML
                import re
                arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                # تمیز کردن فضاهای اضافی
                arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                print(f"متن عربی تمیز شده: {len(arabic_text)} کاراکتر")
            else:
                print("❌ element عربی یافت نشد")
                
            # استخراج متن ترجمه از p با کلاس card-text translate-text
            translate_element = await main_content_div.query_selector('p.card-text.translate-text')
            if translate_element:
                print("✅ element ترجمه یافت شد")
                # استخراج HTML برای حفظ ساختار
                translate_html = await translate_element.inner_html()
                print(f"HTML ترجمه: {translate_html[:200]}...")
                
                # تمیز کردن HTML و تبدیل <br> به \n
                translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                # حذف لینک‌های پاورقی
                import re
                translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                # حذف سایر تگ‌های HTML
                translate_text = re.sub(r'<[^>]+>', '', translate_text)
                # تمیز کردن فضاهای اضافی
                translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                print(f"متن ترجمه تمیز شده: {len(translate_text)} کاراکتر")
            else:
                print("❌ element ترجمه یافت نشد")
        else:
            print("❌ div اصلی یافت نشد")
            
            # روش جایگزین: جستجو مستقیم برای عناصر
            print("🔧 جستجو مستقیم برای عناصر...")
            
            arabic_elements = await page.query_selector_all('p.card-text.arabic-text')
            translate_elements = await page.query_selector_all('p.card-text.translate-text')
            
            print(f"تعداد عناصر عربی: {len(arabic_elements)}")
            print(f"تعداد عناصر ترجمه: {len(translate_elements)}")
            
            if arabic_elements:
                arabic_texts = []
                for element in arabic_elements:
                    text = await element.inner_text()
                    if text.strip():
                        arabic_texts.append(text.strip())
                arabic_text = '\n'.join(arabic_texts)
                
            if translate_elements:
                translate_texts = []
                for element in translate_elements:
                    text = await element.inner_text()
                    if text.strip():
                        translate_texts.append(text.strip())
                translate_text = '\n'.join(translate_texts)

        result = {
            'arabic_text': arabic_text,
            'translate_text': translate_text
        }
        
        print(f"🔍 نتیجه نهایی:")
        print(f"   عربی: {len(result['arabic_text'])} کاراکتر")
        print(f"   ترجمه: {len(result['translate_text'])} کاراکتر")
        
        return result

# تست دستی برای اجرا
async def run_test():
    """اجرای تست دستی"""
    test_instance = TestWisdom1434()
    
    print("🔴 RED Phase: اجرای تست با کد فعلی")
    try:
        await test_instance.test_wisdom_1434_should_have_translation()
        print("🎉 تست موفق شد!")
    except AssertionError as e:
        print(f"❌ تست fail شد (انتظار می‌رفت): {e}")
    except Exception as e:
        print(f"❌ خطای غیرمنتظره: {e}")

if __name__ == "__main__":
    asyncio.run(run_test())
