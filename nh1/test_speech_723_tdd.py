#!/usr/bin/env python3
"""
TDD Test برای رفع مشکل خطبه 723 - کانتینر اول
"""

import asyncio
import pytest
from playwright.async_api import async_playwright

class TestSpeech723:
    """تست‌های TDD برای خطبه 723"""
    
    @pytest.fixture
    async def browser_page(self):
        """فیکسچر برای مرورگر و صفحه"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
            yield page
            await browser.close()
    
    async def test_speech_723_first_container_should_have_translation(self):
        """تست: کانتینر اول خطبه 723 باید ترجمه داشته باشد"""
        
        # Arrange
        url = "http://nahj.makarem.ir/speech/723"
        expected_arabic_contains = "کلام"  # بخش ساده‌تر برای جلوگیری از مشکل کاراکترها
        expected_translation_contains = "از سخنان امام (عليه السلام) است"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
            
            try:
                # Act
                result = await self.extract_first_container(page, url)
                
                # Assert
                assert result is not None, "کانتینر اول نباید None باشد"
                assert result['arabic_text'], "متن عربی نباید خالی باشد"
                assert result['translate_text'], "متن ترجمه نباید خالی باشد"
                assert expected_arabic_contains in result['arabic_text'], f"متن عربی باید شامل '{expected_arabic_contains}' باشد"
                assert expected_translation_contains in result['translate_text'], f"ترجمه باید شامل '{expected_translation_contains}' باشد"
                
                print(f"✅ تست موفق:")
                print(f"   عربی: {result['arabic_text'][:50]}...")
                print(f"   ترجمه: {result['translate_text'][:50]}...")
                
            finally:
                await browser.close()
    
    async def extract_first_container(self, page, url):
        """استخراج کانتینر اول - نسخه بهبود یافته"""

        await page.goto(url, wait_until='networkidle')
        await page.wait_for_timeout(2000)

        # گرفتن کانتینر اول
        first_container = await page.query_selector('.container-fluid article')
        if not first_container:
            return None

        # استخراج متن عربی
        arabic_elements = await first_container.query_selector_all('.arabic-text p, .arabic-text')
        arabic_texts = []
        print(f"🔍 تعداد عناصر عربی: {len(arabic_elements)}")
        for i, elem in enumerate(arabic_elements):
            text = await elem.inner_text()
            print(f"   عنصر {i+1}: '{text[:50]}...'")
            if text.strip():
                arabic_texts.append(text.strip())

        # استخراج متن ترجمه - روش بهبود یافته
        translate_elements = await first_container.query_selector_all('.translate-text p, .translate-text')
        translate_texts = []
        for elem in translate_elements:
            text = await elem.inner_text()
            if text.strip():
                translate_texts.append(text.strip())

        # اگر ترجمه یافت نشد، جستجو برای p های بدون کلاس که شامل "امام" هستند
        if not translate_texts:
            phrase_container = await first_container.query_selector('.phrase-text-container')
            if phrase_container:
                all_p_elements = await phrase_container.query_selector_all('p')
                for elem in all_p_elements:
                    class_attr = await elem.get_attribute('class')
                    text = await elem.inner_text()
                    # اگر p بدون کلاس است و شامل "امام" و "عليه السلام" است
                    if (not class_attr or class_attr == 'None') and text.strip():
                        if 'امام' in text and 'عليه السلام' in text:
                            translate_texts.append(text.strip())
                            print(f"🔧 متن ترجمه از p بدون کلاس یافت شد: {len(text)} کاراکتر")

        result = {
            'arabic_text': '\n'.join(arabic_texts) if arabic_texts else '',
            'translate_text': '\n'.join(translate_texts) if translate_texts else ''
        }

        print(f"🔍 نتیجه نهایی:")
        print(f"   عربی: '{result['arabic_text'][:100]}...'")
        print(f"   ترجمه: '{result['translate_text'][:100]}...'")

        return result

# تست دستی برای اجرا
async def run_test():
    """اجرای تست دستی"""
    test_instance = TestSpeech723()
    
    print("🟢 GREEN Phase: اجرای تست با کد بهبود یافته (انتظار می‌رود pass کند)")
    try:
        await test_instance.test_speech_723_first_container_should_have_translation()
        print("🎉 تست موفق شد!")
    except AssertionError as e:
        print(f"❌ تست هنوز fail می‌کند: {e}")
    except Exception as e:
        print(f"❌ خطای غیرمنتظره: {e}")

if __name__ == "__main__":
    asyncio.run(run_test())
