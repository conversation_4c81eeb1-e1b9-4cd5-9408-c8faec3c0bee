#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت بررسی پیشرفت استخراج خطبه‌ها
"""

import json
import os

def check_progress():
    output_file = "nahj_speeches.json"
    
    if not os.path.exists(output_file):
        print("فایل JSON هنوز ایجاد نشده است.")
        return
    
    try:
        with open(output_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        total_speeches = data['information']['total_speech']
        total_containers = data['information']['total_section_containers']
        total_pages = data['information']['total_pages']
        
        print(f"📊 گزارش پیشرفت:")
        print(f"   📖 تعداد خطبه‌های استخراج شده: {total_speeches}")
        print(f"   📄 تعداد صفحات: {total_pages}")
        print(f"   📝 تعداد کل بخش‌ها: {total_containers}")
        
        if total_speeches > 0:
            print(f"\n🔍 نمونه خطبه‌های استخراج شده:")
            for i, speech in enumerate(data['speechs'][:5]):
                print(f"   {i+1}. خطبه {speech['number']}: {speech['title']}")
                print(f"      📍 صفحه: {speech['page']}, بخش‌ها: {speech['total_section_phrase_container']}")
        
        # محاسبه درصد پیشرفت (تخمینی بر اساس 241 خطبه)
        estimated_total = 241
        progress_percent = (total_speeches / estimated_total) * 100
        print(f"\n📈 پیشرفت تخمینی: {progress_percent:.1f}% ({total_speeches}/{estimated_total})")
        
    except Exception as e:
        print(f"خطا در خواندن فایل: {e}")

if __name__ == "__main__":
    check_progress()
