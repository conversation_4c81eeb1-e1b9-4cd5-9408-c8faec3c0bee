#!/usr/bin/env python3
"""
تست استخراج حکمت 1389 برای بررسی رفع مشکل
"""

import asyncio
import json
from playwright.async_api import async_playwright
import logging

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_wisdom_1389():
    """تست استخراج حکمت 1389"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        url = "http://nahj.makarem.ir/wisdom/1389"
        logger.info(f"در حال بارگذاری صفحه: {url}")
        
        try:
            await page.goto(url, wait_until='domcontentloaded')
            await page.wait_for_timeout(1000)  # انتظار برای بارگذاری کامل
            
            arabic_text = ""
            translate_text = ""
            
            # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
            main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
            
            if main_content_div:
                logger.info("div اصلی یافت شد")
                
                # استخراج متن عربی از p با کلاس arabic-text
                arabic_element = await main_content_div.query_selector('p.arabic-text')
                if arabic_element:
                    logger.info("element عربی یافت شد")
                    # استخراج HTML برای حفظ ساختار
                    arabic_html = await arabic_element.inner_html()
                    # تمیز کردن HTML و تبدیل <br> به \n
                    arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    # حذف سایر تگ‌های HTML
                    import re
                    arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                    # تمیز کردن فضاهای اضافی
                    arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                else:
                    logger.warning("element عربی یافت نشد")
                    
                # استخراج متن ترجمه از p با کلاس translate-text
                translate_element = await main_content_div.query_selector('p.translate-text')
                if translate_element:
                    logger.info("element ترجمه یافت شد")
                    # استخراج HTML برای حفظ ساختار
                    translate_html = await translate_element.inner_html()
                    # تمیز کردن HTML و تبدیل <br> به \n
                    translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    # حذف سایر تگ‌های HTML (به جز لینک‌های پاورقی)
                    import re
                    # حذف لینک‌های پاورقی
                    translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                    # حذف سایر تگ‌های HTML
                    translate_text = re.sub(r'<[^>]+>', '', translate_text)
                    # تمیز کردن فضاهای اضافی
                    translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                else:
                    logger.warning("element ترجمه یافت نشد")
            else:
                logger.warning("div اصلی یافت نشد")
                
                # جستجو برای تمام p elements با کلاس‌های مربوطه
                arabic_elements = await page.query_selector_all('p.arabic-text')
                translate_elements = await page.query_selector_all('p.translate-text')
                
                logger.info(f"تعداد elements عربی: {len(arabic_elements)}")
                logger.info(f"تعداد elements ترجمه: {len(translate_elements)}")
                
                if arabic_elements:
                    arabic_texts = []
                    for element in arabic_elements:
                        text = await element.inner_text()
                        if text.strip():
                            arabic_texts.append(text.strip())
                    arabic_text = '\n'.join(arabic_texts)
                    
                if translate_elements:
                    translate_texts = []
                    for element in translate_elements:
                        text = await element.inner_text()
                        if text.strip():
                            translate_texts.append(text.strip())
                    translate_text = '\n'.join(translate_texts)
            
            # نمایش نتایج
            print("\n" + "="*50)
            print("نتایج استخراج حکمت 1389:")
            print("="*50)
            
            print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
            print("-" * 30)
            print(arabic_text[:500] + "..." if len(arabic_text) > 500 else arabic_text)
            
            print(f"\nمتن ترجمه ({len(translate_text)} کاراکتر):")
            print("-" * 30)
            print(translate_text[:500] + "..." if len(translate_text) > 500 else translate_text)
            
            # ذخیره در فایل JSON برای بررسی
            result = {
                "number": 1389,
                "url": url,
                "arabic_text": arabic_text,
                "translate_text": translate_text,
                "arabic_length": len(arabic_text),
                "translate_length": len(translate_text)
            }
            
            with open('test_wisdom_1389_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            logger.info("نتایج در فایل test_wisdom_1389_result.json ذخیره شد")
            
        except Exception as e:
            logger.error(f"خطا در تست: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_wisdom_1389())
