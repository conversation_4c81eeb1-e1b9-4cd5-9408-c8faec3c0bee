#!/usr/bin/env python3
"""
TDD Test برای رفع مشکل حکمت 1048 - هیچ کانتینری یافت نشده
"""

import asyncio
from playwright.async_api import async_playwright

class TestWisdom1048:
    """تست‌های TDD برای حکمت 1048"""
    
    async def test_wisdom_1048_should_have_container(self):
        """تست: حکمت 1048 باید حداقل یک کانتینر داشته باشد"""
        
        # Arrange
        url = "http://nahj.makarem.ir/wisdom/1048"
        expected_arabic_contains = "قَالَ عَلَيهِ السِّلَامُ"
        expected_translation_contains = "امام (عليه السلام) فرمود"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
            
            try:
                # Act
                result = await self.extract_wisdom_with_current_logic(page, url)
                
                # Assert
                assert result is not None, "نتیجه نباید None باشد"
                assert result['containers'], "باید حداقل یک کانتینر داشته باشد"
                assert len(result['containers']) > 0, "تعداد کانتینرها باید بیشتر از صفر باشد"
                
                first_container = result['containers'][0]
                assert first_container['arabic-text']['main'], "متن عربی نباید خالی باشد"
                assert first_container['translate-text']['main'], "متن ترجمه نباید خالی باشد"
                
                print(f"✅ تست موفق:")
                print(f"   تعداد کانتینرها: {len(result['containers'])}")
                print(f"   عربی: {len(first_container['arabic-text']['main'])} کاراکتر")
                print(f"   ترجمه: {len(first_container['translate-text']['main'])} کاراکتر")
                
            finally:
                await browser.close()
    
    async def extract_wisdom_with_current_logic(self, page, url):
        """استخراج حکمت با منطق فعلی اسکریپت اصلی"""
        
        await page.goto(url, wait_until='networkidle')
        await page.wait_for_timeout(2000)
        
        containers = []
        
        print("🔍 شبیه‌سازی منطق اسکریپت اصلی:")
        print("="*60)
        
        # شبیه‌سازی منطق اسکریپت اصلی برای wisdom
        arabic_text = ""
        translate_text = ""
        
        # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
        main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
        
        if main_content_div:
            print("✅ div اصلی (.col-lg-7.col-sm-11) یافت شد")
            
            # استخراج متن عربی از p با کلاس card-text arabic-text
            arabic_element = await main_content_div.query_selector('p.card-text.arabic-text')
            if arabic_element:
                print("✅ element عربی یافت شد")
                arabic_html = await arabic_element.inner_html()
                arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                import re
                arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                print(f"   متن عربی: {len(arabic_text)} کاراکتر")
            else:
                print("❌ element عربی یافت نشد")
                
            # استخراج متن ترجمه از p با کلاس card-text translate-text
            translate_element = await main_content_div.query_selector('p.card-text.translate-text')
            if translate_element:
                print("✅ element ترجمه یافت شد")
                translate_html = await translate_element.inner_html()
                translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                import re
                translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                translate_text = re.sub(r'<[^>]+>', '', translate_text)
                translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                print(f"   متن ترجمه: {len(translate_text)} کاراکتر")
            else:
                print("❌ element ترجمه یافت نشد")
        else:
            print("❌ div اصلی یافت نشد")
        
        # اگر محتوا یافت شد، کانتینر بساز
        if arabic_text or translate_text:
            container_data = {
                "number": 1,
                "header": "",
                "arabic-text": {"main": arabic_text, "metadata": None},
                "translate-text": {"main": translate_text, "metadata": None}
            }
            containers.append(container_data)
            print(f"✅ کانتینر ساخته شد")
        else:
            print("❌ هیچ محتوایی یافت نشد - کانتینری ساخته نمی‌شود")
        
        print(f"\n🔍 نتیجه نهایی: {len(containers)} کانتینر")
        
        return {
            'containers': containers,
            'total_section_phrase_container': len(containers)
        }

# تست دستی برای اجرا
async def run_test():
    """اجرای تست دستی"""
    test_instance = TestWisdom1048()
    
    print("🔴 RED Phase: اجرای تست با منطق فعلی")
    try:
        await test_instance.test_wisdom_1048_should_have_container()
        print("🎉 تست موفق شد!")
    except AssertionError as e:
        print(f"❌ تست fail شد (انتظار می‌رفت): {e}")
    except Exception as e:
        print(f"❌ خطای غیرمنتظره: {e}")

if __name__ == "__main__":
    asyncio.run(run_test())
