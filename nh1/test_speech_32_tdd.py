#!/usr/bin/env python3
"""
TDD Test برای رفع مشکل خطبه 32 - کانتینر اول
"""

import asyncio
from playwright.async_api import async_playwright

class TestSpeech32:
    """تست‌های TDD برای خطبه 32"""
    
    async def test_speech_32_first_container_should_have_translation(self):
        """تست: کانتینر اول خطبه 32 باید ترجمه داشته باشد"""
        
        # Arrange
        url = "http://nahj.makarem.ir/speech/32"
        expected_arabic_contains = "خُطبَةٍ لَهُ عَليهِ السَّلامُ"
        expected_translation_contains = "از خطبه هاى امام"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
            
            try:
                # Act
                result = await self.extract_first_container_current_method(page, url)
                
                # Assert
                assert result is not None, "کانتینر اول نباید None باشد"
                assert result['arabic_text'], "متن عربی نباید خالی باشد"
                assert result['translate_text'], "متن ترجمه نباید خالی باشد"
                assert expected_translation_contains in result['translate_text'], f"ترجمه باید شامل '{expected_translation_contains}' باشد"
                
                print(f"✅ تست موفق:")
                print(f"   عربی: {len(result['arabic_text'])} کاراکتر")
                print(f"   ترجمه: {len(result['translate_text'])} کاراکتر")
                print(f"   ترجمه: {result['translate_text'][:100]}...")
                
            finally:
                await browser.close()
    
    async def extract_first_container_current_method(self, page, url):
        """استخراج کانتینر اول با روش فعلی"""
        
        await page.goto(url, wait_until='networkidle')
        await page.wait_for_timeout(2000)
        
        # گرفتن کانتینر اول
        first_container = await page.query_selector('.container-fluid article')
        if not first_container:
            return None
        
        # استخراج متن عربی
        arabic_elements = await first_container.query_selector_all('.arabic-text p, .arabic-text')
        arabic_texts = []
        print(f"🔍 تعداد عناصر عربی: {len(arabic_elements)}")
        for i, elem in enumerate(arabic_elements):
            text = await elem.inner_text()
            print(f"   عربی {i+1}: {len(text)} کاراکتر")
            if text.strip():
                arabic_texts.append(text.strip())
        
        # استخراج متن ترجمه - روش فعلی
        translate_elements = await first_container.query_selector_all('.translate-text p, .translate-text')
        translate_texts = []
        print(f"🔍 تعداد عناصر ترجمه: {len(translate_elements)}")
        for i, elem in enumerate(translate_elements):
            text = await elem.inner_text()
            print(f"   ترجمه {i+1}: '{text[:50]}...' ({len(text)} کاراکتر)")
            if text.strip():
                translate_texts.append(text.strip())
        
        # اگر ترجمه یافت نشد، جستجو برای p بعد از translate-text خالی
        if not translate_texts:
            print("🔧 جستجو برای p بعد از translate-text خالی...")
            phrase_container = await first_container.query_selector('.phrase-text-container')
            if phrase_container:
                # پیدا کردن translate-text خالی
                translate_element = await phrase_container.query_selector('p.translate-text')
                if translate_element:
                    translate_text_content = await translate_element.inner_text()
                    print(f"   translate-text یافت شد: '{translate_text_content}' (خالی: {not translate_text_content.strip()})")
                    
                    # گرفتن تمام p های بعد از translate-text
                    all_p_elements = await phrase_container.query_selector_all('p')
                    print(f"   تعداد کل p ها: {len(all_p_elements)}")
                    
                    translate_found = False
                    for i, elem in enumerate(all_p_elements, 1):
                        class_attr = await elem.get_attribute('class')
                        text = await elem.inner_text()
                        print(f"   p{i}: class='{class_attr}', text='{text[:30]}...' ({len(text)} کاراکتر)")
                        
                        # اگر به translate-text رسیدیم، فلگ را فعال کن
                        if class_attr == 'translate-text':
                            translate_found = True
                            print(f"      🎯 translate-text یافت شد")
                            continue
                        
                        # اگر بعد از translate-text هستیم و p بدون کلاس است
                        if translate_found and (not class_attr or class_attr == 'None'):
                            if text.strip():
                                translate_texts.append(text.strip())
                                print(f"      ✅ متن ترجمه یافت شد: {len(text)} کاراکتر")
                                break  # فقط اولین p را بگیر
        
        result = {
            'arabic_text': '\n'.join(arabic_texts) if arabic_texts else '',
            'translate_text': '\n'.join(translate_texts) if translate_texts else ''
        }
        
        print(f"🔍 نتیجه نهایی:")
        print(f"   عربی: {len(result['arabic_text'])} کاراکتر")
        print(f"   ترجمه: {len(result['translate_text'])} کاراکتر")
        
        return result

# تست دستی برای اجرا
async def run_test():
    """اجرای تست دستی"""
    test_instance = TestSpeech32()
    
    print("🔴 RED Phase: اجرای تست با کد فعلی")
    try:
        await test_instance.test_speech_32_first_container_should_have_translation()
        print("🎉 تست موفق شد!")
    except AssertionError as e:
        print(f"❌ تست fail شد (انتظار می‌رفت): {e}")
    except Exception as e:
        print(f"❌ خطای غیرمنتظره: {e}")

if __name__ == "__main__":
    asyncio.run(run_test())
