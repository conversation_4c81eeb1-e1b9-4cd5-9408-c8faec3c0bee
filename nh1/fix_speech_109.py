#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اسکریپت رفع مشکل خطبه 109 (خطبه 37)
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_speech_109():
    """رفع مشکل خطبه 109"""
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            url = "http://nahj.makarem.ir/speech/109"
            logger.info(f"در حال بررسی خطبه 109: {url}")
            
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            containers = []
            
            # روش جدید: جستجوی مستقیم برای h4 ها
            h4_elements = await page.query_selector_all('h4')
            logger.info(f"تعداد h4 های یافت شده: {len(h4_elements)}")
            
            for i, h4_element in enumerate(h4_elements, 1):
                try:
                    header = await h4_element.inner_text()
                    logger.info(f"پردازش h4 شماره {i}: {header}")
                    
                    # پیدا کردن والد که شامل محتوا است
                    parent = h4_element
                    for _ in range(3):  # بررسی تا 3 سطح والد
                        parent = await parent.query_selector('xpath=..')
                        if not parent:
                            break
                        
                        # جستجو برای متن عربی
                        arabic_element = await parent.query_selector('.arabic-text')
                        if arabic_element:
                            break
                    
                    arabic_text = ""
                    translate_text = ""
                    
                    if parent:
                        # جستجو برای متن عربی
                        arabic_element = await parent.query_selector('.arabic-text')
                        if arabic_element:
                            arabic_text = await arabic_element.inner_text()
                        
                        # جستجو برای متن ترجمه
                        translate_element = await parent.query_selector('.translate-text')
                        if translate_element:
                            translate_text = await translate_element.inner_text()
                    
                    # اگر متن پیدا نشد، روش دیگری امتحان کن
                    if not arabic_text or not translate_text:
                        logger.info(f"جستجوی جایگزین برای بخش {i}")
                        
                        # استفاده از JavaScript برای پیدا کردن متن
                        result = await page.evaluate('''(h4) => {
                            const result = {arabic: '', translate: ''};
                            
                            // پیدا کردن article والد
                            let article = h4.closest('article');
                            if (!article) {
                                article = h4.parentElement;
                                while (article && article.tagName !== 'ARTICLE') {
                                    article = article.parentElement;
                                }
                            }
                            
                            if (article) {
                                // جستجو برای متن عربی
                                const arabicEl = article.querySelector('.arabic-text');
                                if (arabicEl) {
                                    result.arabic = arabicEl.textContent.trim();
                                }
                                
                                // جستجو برای متن ترجمه
                                const translateEl = article.querySelector('.translate-text');
                                if (translateEl) {
                                    result.translate = translateEl.textContent.trim();
                                }
                                
                                // اگر پیدا نشد، در p های بعدی جستجو کن
                                if (!result.arabic || !result.translate) {
                                    let current = h4.nextElementSibling;
                                    while (current) {
                                        if (current.tagName === 'P') {
                                            const text = current.textContent.trim();
                                            // تشخیص متن عربی (حاوی حروف عربی)
                                            if (!result.arabic && /[\u0600-\u06FF]/.test(text) && text.length > 20) {
                                                result.arabic = text;
                                            }
                                            // تشخیص متن فارسی (بدون حروف عربی زیاد)
                                            else if (!result.translate && text.length > 20 && !/^[\u0600-\u06FF\s]+$/.test(text)) {
                                                result.translate = text;
                                            }
                                        }
                                        current = current.nextElementSibling;
                                        if (result.arabic && result.translate) break;
                                    }
                                }
                            }
                            
                            return result;
                        }''', h4_element)
                        
                        if result.get('arabic'):
                            arabic_text = result['arabic']
                        if result.get('translate'):
                            translate_text = result['translate']
                    
                    logger.info(f"بخش {i}: header='{header[:50]}...', arabic={len(arabic_text)}, translate={len(translate_text)}")
                    
                    if header and (arabic_text or translate_text):
                        container_data = {
                            "number": i,
                            "header": header.strip(),
                            "arabic-text": arabic_text.strip(),
                            "translate-text": translate_text.strip()
                        }
                        containers.append(container_data)
                        logger.info(f"✅ بخش {i} با موفقیت استخراج شد")
                    else:
                        logger.warning(f"⚠️ بخش {i} خالی است")
                        
                except Exception as e:
                    logger.error(f"❌ خطا در استخراج بخش {i}: {e}")
                    continue
            
            logger.info(f"🎉 مجموع {len(containers)} بخش استخراج شد")
            
            # نمایش نتایج
            for container in containers:
                print(f"\n--- بخش {container['number']}: {container['header']} ---")
                print(f"متن عربی: {container['arabic-text'][:100]}...")
                print(f"ترجمه: {container['translate-text'][:100]}...")
            
            # به‌روزرسانی فایل JSON
            if containers:
                await update_json_file(containers)
            
        finally:
            await browser.close()

async def update_json_file(containers):
    """به‌روزرسانی فایل JSON با داده‌های جدید"""
    try:
        # خواندن فایل موجود
        with open('nahj_speeches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # پیدا کردن خطبه 109 و به‌روزرسانی آن
        for speech in data['speechs']:
            if speech['number'] == 109:
                speech['containers'] = containers
                speech['total_section_phrase_container'] = len(containers)
                logger.info(f"✅ خطبه 109 با {len(containers)} بخش به‌روزرسانی شد")
                break
        
        # به‌روزرسانی آمار کل
        total_containers = sum(speech['total_section_phrase_container'] for speech in data['speechs'])
        data['information']['total_section_containers'] = total_containers
        
        # ذخیره فایل
        with open('nahj_speeches.json', 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info("✅ فایل JSON با موفقیت به‌روزرسانی شد")
        
    except Exception as e:
        logger.error(f"❌ خطا در به‌روزرسانی فایل JSON: {e}")

async def main():
    await fix_speech_109()

if __name__ == "__main__":
    asyncio.run(main())
