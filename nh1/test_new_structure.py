#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست ساختار جدید برای arabic-text و translate-text
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_single_speech():
    """تست یک خطبه خاص"""
    
    scraper = NahjScraper()
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            # تست خطبه 109 که قبلاً مشکل داشت
            speech_info = {
                "number": 109,
                "page": 1,
                "title": "خطبه 37 - ذکر فضائل خود",
                "url": "http://nahj.makarem.ir/speech/109",
                "total_section_phrase_container": 0,
                "containers": []
            }
            
            logger.info("🧪 تست ساختار جدید...")
            
            # استخراج محتوا
            result = await scraper.extract_speech_content(page, speech_info)
            
            # نمایش نتایج
            print(f"\n📖 خطبه {result['number']}: {result['title']}")
            print(f"📊 تعداد بخش‌ها: {result['total_section_phrase_container']}")
            
            for container in result['containers']:
                print(f"\n--- بخش {container['number']}: {container['header']} ---")
                
                # نمایش ساختار arabic-text
                arabic = container['arabic-text']
                print(f"🔤 متن عربی:")
                print(f"   📝 Main ({len(arabic['main'])} کاراکتر): {arabic['main'][:100]}...")
                if arabic['metadata']:
                    print(f"   📋 Metadata ({len(arabic['metadata'])} پاراگراف):")
                    for i, meta in enumerate(arabic['metadata'], 1):
                        print(f"      {i}. ({len(meta)} کاراکتر): {meta[:50]}...")
                else:
                    print(f"   📋 Metadata: None")
                
                # نمایش ساختار translate-text
                translate = container['translate-text']
                print(f"🔤 متن ترجمه:")
                print(f"   📝 Main ({len(translate['main'])} کاراکتر): {translate['main'][:100]}...")
                if translate['metadata']:
                    print(f"   📋 Metadata ({len(translate['metadata'])} پاراگراف):")
                    for i, meta in enumerate(translate['metadata'], 1):
                        print(f"      {i}. ({len(meta)} کاراکتر): {meta[:50]}...")
                else:
                    print(f"   📋 Metadata: None")
            
            # ذخیره نمونه JSON
            with open('test_output.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            
            print(f"\n✅ تست موفقیت‌آمیز! نتیجه در test_output.json ذخیره شد.")
            
        finally:
            await browser.close()

async def test_process_text_content():
    """تست تابع process_text_content"""
    
    scraper = NahjScraper()
    
    print("🧪 تست تابع process_text_content:")
    
    # تست 1: یک پاراگراف
    test1 = ["این یک پاراگراف تست است"]
    result1 = scraper.process_text_content(test1)
    print(f"\n📝 تست 1 (یک پاراگراف):")
    print(f"   Input: {test1}")
    print(f"   Output: {result1}")
    
    # تست 2: چند پاراگراف
    test2 = [
        "پاراگراف کوتاه",
        "این پاراگراف بلندتری است که باید به عنوان main انتخاب شود چون بیشترین کاراکتر را دارد",
        "پاراگراف متوسط"
    ]
    result2 = scraper.process_text_content(test2)
    print(f"\n📝 تست 2 (چند پاراگراف):")
    print(f"   Input: {len(test2)} پاراگراف")
    print(f"   Main: {result2['main'][:50]}...")
    print(f"   Metadata: {len(result2['metadata']) if result2['metadata'] else 0} پاراگراف")
    
    # تست 3: لیست خالی
    test3 = []
    result3 = scraper.process_text_content(test3)
    print(f"\n📝 تست 3 (لیست خالی):")
    print(f"   Input: {test3}")
    print(f"   Output: {result3}")

async def main():
    print("🚀 شروع تست ساختار جدید")
    
    # تست تابع پردازش
    await test_process_text_content()
    
    # تست استخراج واقعی
    await test_single_speech()
    
    print("\n🎉 تمام تست‌ها تکمیل شد!")

if __name__ == "__main__":
    asyncio.run(main())
