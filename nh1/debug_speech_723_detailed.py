#!/usr/bin/env python3
"""
بررسی دقیق HTML کانتینر اول خطبه 723
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_first_container():
    """بررسی دقیق کانتینر اول"""
    
    url = "http://nahj.makarem.ir/speech/723"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            # گرفتن کانتینر اول
            first_container = await page.query_selector('.container-fluid article')
            phrase_container = await first_container.query_selector('.phrase-text-container')
            
            # نمایش HTML کامل
            html = await phrase_container.inner_html()
            print("HTML کامل کانتینر اول:")
            print("="*80)
            print(html)
            print("="*80)
            
            # بررسی تمام عناصر p
            all_p = await phrase_container.query_selector_all('p')
            print(f"\nتعداد کل عناصر p: {len(all_p)}")
            
            for i, p in enumerate(all_p, 1):
                class_attr = await p.get_attribute('class')
                style_attr = await p.get_attribute('style')
                text = await p.inner_text()
                html_content = await p.inner_html()
                
                print(f"\nعنصر p شماره {i}:")
                print(f"  class: {class_attr}")
                print(f"  style: {style_attr}")
                print(f"  text: {text[:100]}...")
                print(f"  html: {html_content[:100]}...")
                
                # بررسی اینکه آیا این متن ترجمه است
                if text.strip() and 'امام' in text and 'عليه السلام' in text:
                    print(f"  🎯 این احتمالاً متن ترجمه است!")
            
        except Exception as e:
            print(f"خطا: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_first_container())
