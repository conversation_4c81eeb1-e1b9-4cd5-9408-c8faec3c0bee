#!/usr/bin/env python3
"""
تست اسکریپت اصلی برای حکمت 1048
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_main_scraper_wisdom_1048():
    """تست اسکریپت اصلی برای حکمت 1048"""
    
    wisdom_info = {
        "number": 1048,
        "url": "http://nahj.makarem.ir/wisdom/1048",
        "title": "حکمت 89 - بستوه نيامدن از علم و دانش 1"
    }
    
    scraper = NahjScraper("wisdom")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info(f"🧪 تست اسکریپت اصلی برای حکمت {wisdom_info['number']}")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, wisdom_info)
            
            print("\n" + "="*80)
            print(f"نتایج استخراج حکمت {wisdom_info['number']} با اسکریپت اصلی:")
            print("="*80)
            
            print(f"نتیجه: {result}")
            
            if result and 'containers' in result:
                print(f"تعداد کانتینرها: {len(result['containers'])}")
                print(f"total_section_phrase_container: {result.get('total_section_phrase_container', 'نامشخص')}")
                
                if result['containers']:
                    # بررسی کانتینر اول
                    first_container = result['containers'][0]
                    arabic_text = first_container.get('arabic-text', {}).get('main', '')
                    translate_text = first_container.get('translate-text', {}).get('main', '')
                    header = first_container.get('header', '')
                    
                    print(f"\n🔍 کانتینر اول:")
                    print(f"  Header: '{header}'")
                    print(f"  عربی: {len(arabic_text)} کاراکتر")
                    print(f"  ترجمه: {len(translate_text)} کاراکتر")
                    print(f"  عربی: '{arabic_text[:50]}...'")
                    print(f"  ترجمه: '{translate_text[:50]}...'")
                    
                    print("\n✅ اسکریپت اصلی به درستی کار می‌کند!")
                    return True
                else:
                    print("\n❌ هیچ کانتینری یافت نشد!")
                    return False
            else:
                print("❌ نتیجه خالی یا نامعتبر است")
                print(f"نوع نتیجه: {type(result)}")
                return False
                
        except Exception as e:
            logger.error(f"❌ خطا در تست: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
            return False
        
        finally:
            await browser.close()

if __name__ == "__main__":
    result = asyncio.run(test_main_scraper_wisdom_1048())
    if result:
        print("\n🎯 اسکریپت اصلی آماده است!")
    else:
        print("\n⚠️ نیاز به بررسی بیشتر دارد.")
