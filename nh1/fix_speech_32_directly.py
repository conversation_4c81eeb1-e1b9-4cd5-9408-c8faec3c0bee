#!/usr/bin/env python3
"""
رفع مستقیم خطبه 32
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_speech_32_directly():
    """رفع مستقیم خطبه 32"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_speeches.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن خطبه 32
    speech_32 = None
    speech_index = -1
    for i, speech in enumerate(data['speechs']):
        if speech['number'] == 32:
            speech_32 = speech
            speech_index = i
            break
    
    if not speech_32:
        logger.error("خطبه 32 یافت نشد!")
        return
    
    logger.info(f"خطبه 32 یافت شد: {speech_32['title']}")
    logger.info(f"URL: {speech_32['url']}")
    
    # بررسی وضعیت فعلی کانتینر اول
    if speech_32['containers']:
        first_container = speech_32['containers'][0]
        current_translate = first_container.get('translate-text', {}).get('main', '')
        logger.info(f"وضعیت فعلی کانتینر اول: ترجمه {len(current_translate)} کاراکتر")
        
        if current_translate:
            logger.info("کانتینر اول قبلاً دارای ترجمه است. خروج...")
            return
    
    # استخراج محتوا با اسکریپت اصلی
    scraper = NahjScraper("speech")
    
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            logger.info("در حال استخراج محتوا با اسکریپت بهبود یافته...")
            
            # استفاده از متد extract_content از اسکریپت اصلی
            result = await scraper.extract_content(page, speech_32)
            
            if result and 'containers' in result and result['containers'] and len(result['containers']) > 0:
                new_first_container = result['containers'][0]
                new_translate = new_first_container.get('translate-text', {}).get('main', '')
                
                if new_translate:
                    # به‌روزرسانی کانتینر اول در داده
                    data['speechs'][speech_index]['containers'][0] = new_first_container
                    
                    # ذخیره فایل
                    with open('nahj_speeches.json', 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2)
                    
                    logger.info(f"✅ خطبه 32 با موفقیت رفع شد!")
                    logger.info(f"کانتینر اول - ترجمه: {len(new_translate)} کاراکتر")
                    
                    # نمایش نمونه محتوا
                    print("\n" + "="*60)
                    print("محتوای رفع شده:")
                    print("="*60)
                    
                    arabic_text = new_first_container.get('arabic-text', {}).get('main', '')
                    print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
                    print("-" * 40)
                    print(arabic_text[:200] + "..." if len(arabic_text) > 200 else arabic_text)
                    
                    print(f"\nمتن ترجمه ({len(new_translate)} کاراکتر):")
                    print("-" * 40)
                    print(new_translate[:200] + "..." if len(new_translate) > 200 else new_translate)
                    
                else:
                    logger.error("❌ اسکریپت جدید نتوانست ترجمه استخراج کند")
            else:
                logger.error("❌ اسکریپت جدید هیچ کانتینری استخراج نکرد")
                
        except Exception as e:
            logger.error(f"خطا در استخراج: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(fix_speech_32_directly())
