#!/usr/bin/env python3
"""
دیباگ مشکل حکمت 1189
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
import re

# تنظیم لاگ
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_wisdom_1189():
    """دیباگ مشکل حکمت 1189"""
    
    url = "http://nahj.makarem.ir/wisdom/1189"
    logger.info(f"شروع دیباگ حکمت 1189: {url}")
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            await page.goto(url, wait_until='domcontentloaded')
            await page.wait_for_timeout(2000)  # انتظار بیشتر
            
            print("="*60)
            print("مرحله 1: بررسی وجود div اصلی")
            print("="*60)
            
            # بررسی div اصلی
            main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
            if main_content_div:
                print("✅ div اصلی (.col-lg-7.col-sm-11) یافت شد")
                
                # بررسی محتوای کامل div
                div_html = await main_content_div.inner_html()
                print(f"محتوای HTML div اصلی ({len(div_html)} کاراکتر):")
                print("-" * 40)
                print(div_html[:500] + "..." if len(div_html) > 500 else div_html)
                print("-" * 40)
                
            else:
                print("❌ div اصلی یافت نشد")
                
                # بررسی تمام div های موجود
                all_divs = await page.query_selector_all('div')
                print(f"تعداد کل div ها: {len(all_divs)}")
                
                for i, div in enumerate(all_divs[:10]):  # نمایش 10 تای اول
                    class_name = await div.get_attribute('class')
                    print(f"div {i+1}: class='{class_name}'")
            
            print("\n" + "="*60)
            print("مرحله 2: بررسی عناصر عربی")
            print("="*60)
            
            # بررسی عناصر عربی
            arabic_elements = await page.query_selector_all('p.arabic-text')
            print(f"تعداد عناصر p.arabic-text: {len(arabic_elements)}")
            
            for i, element in enumerate(arabic_elements):
                text = await element.inner_text()
                html = await element.inner_html()
                print(f"عنصر عربی {i+1}:")
                print(f"  متن: {text[:100]}...")
                print(f"  HTML: {html[:100]}...")
                print()
            
            print("="*60)
            print("مرحله 3: بررسی عناصر ترجمه")
            print("="*60)
            
            # بررسی عناصر ترجمه
            translate_elements = await page.query_selector_all('p.translate-text')
            print(f"تعداد عناصر p.translate-text: {len(translate_elements)}")
            
            for i, element in enumerate(translate_elements):
                text = await element.inner_text()
                html = await element.inner_html()
                print(f"عنصر ترجمه {i+1}:")
                print(f"  متن: {text[:100]}...")
                print(f"  HTML: {html[:100]}...")
                print()
            
            print("="*60)
            print("مرحله 4: بررسی تمام عناصر p")
            print("="*60)
            
            # بررسی تمام عناصر p
            all_p_elements = await page.query_selector_all('p')
            print(f"تعداد کل عناصر p: {len(all_p_elements)}")
            
            for i, element in enumerate(all_p_elements):
                class_name = await element.get_attribute('class')
                text = await element.inner_text()
                if text.strip() and len(text) > 20:  # فقط عناصر با محتوای معنادار
                    print(f"p {i+1}: class='{class_name}', متن: {text[:50]}...")
            
            print("\n" + "="*60)
            print("مرحله 5: تست استخراج با کد فعلی")
            print("="*60)
            
            # تست استخراج با کد فعلی
            arabic_text = ""
            translate_text = ""
            
            main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
            
            if main_content_div:
                # استخراج متن عربی
                arabic_element = await main_content_div.query_selector('p.arabic-text')
                if arabic_element:
                    arabic_html = await arabic_element.inner_html()
                    arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                    arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                    print(f"✅ متن عربی استخراج شد: {len(arabic_text)} کاراکتر")
                    print(f"نمونه: {arabic_text[:100]}...")
                else:
                    print("❌ عنصر عربی در div اصلی یافت نشد")
                    
                # استخراج متن ترجمه
                translate_element = await main_content_div.query_selector('p.translate-text')
                if translate_element:
                    translate_html = await translate_element.inner_html()
                    translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                    translate_text = re.sub(r'<[^>]+>', '', translate_text)
                    translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                    print(f"✅ متن ترجمه استخراج شد: {len(translate_text)} کاراکتر")
                    print(f"نمونه: {translate_text[:100]}...")
                else:
                    print("❌ عنصر ترجمه در div اصلی یافت نشد")
            
            print("\n" + "="*60)
            print("خلاصه نتایج:")
            print("="*60)
            print(f"متن عربی: {'✅ موجود' if arabic_text else '❌ خالی'} ({len(arabic_text)} کاراکتر)")
            print(f"متن ترجمه: {'✅ موجود' if translate_text else '❌ خالی'} ({len(translate_text)} کاراکتر)")
            
        except Exception as e:
            logger.error(f"خطا در دیباگ: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_wisdom_1189())
