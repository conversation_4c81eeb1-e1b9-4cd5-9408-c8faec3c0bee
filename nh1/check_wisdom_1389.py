#!/usr/bin/env python3
"""
بررسی وضعیت حکمت 1389
"""

import json

def check_wisdom_1389():
    """بررسی وضعیت حکمت 1389"""

    # بارگذاری فایل موجود (با force reload)
    try:
        import os
        # اطمینان از خواندن آخرین نسخه فایل
        if os.path.exists('nahj_wisdoms.json'):
            with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
                data = json.load(f)
        else:
            print("فایل nahj_wisdoms.json یافت نشد!")
            return
    except Exception as e:
        print(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت 1389
    wisdom_1389 = None
    for wisdom in data['wisdoms']:
        if wisdom['number'] == 1389:
            wisdom_1389 = wisdom
            break
    
    if not wisdom_1389:
        print("حکمت 1389 یافت نشد!")
        return
    
    print("وضعیت حکمت 1389:")
    print("-" * 50)
    print(f"شماره: {wisdom_1389['number']}")
    print(f"عنوان: {wisdom_1389['title']}")
    print(f"URL: {wisdom_1389['url']}")
    print(f"تعداد کانتینرها: {len(wisdom_1389['containers'])}")
    print(f"total_section_phrase_container: {wisdom_1389['total_section_phrase_container']}")
    
    if wisdom_1389['containers']:
        container = wisdom_1389['containers'][0]
        arabic_text = container.get('arabic-text', {}).get('main', '')
        translate_text = container.get('translate-text', {}).get('main', '')
        
        print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
        print(arabic_text[:200] + "..." if len(arabic_text) > 200 else arabic_text)
        
        print(f"\nمتن ترجمه ({len(translate_text)} کاراکتر):")
        print(translate_text[:200] + "..." if len(translate_text) > 200 else translate_text)
        
        if arabic_text or translate_text:
            print("\n✅ حکمت 1389 دارای محتوا است")
        else:
            print("\n❌ حکمت 1389 خالی است")
    else:
        print("\n❌ حکمت 1389 هیچ کانتینری ندارد")

if __name__ == "__main__":
    check_wisdom_1389()
