#!/usr/bin/env python3
"""
تست نهایی و تمیز برای خطبه 723
"""

import asyncio
from playwright.async_api import async_playwright

async def extract_first_container_improved(page, url):
    """استخراج بهبود یافته کانتینر اول"""
    
    await page.goto(url, wait_until='networkidle')
    await page.wait_for_timeout(2000)
    
    # گرفتن کانتینر اول
    first_container = await page.query_selector('.container-fluid article')
    if not first_container:
        return None
    
    # استخراج متن عربی
    arabic_elements = await first_container.query_selector_all('.arabic-text p, .arabic-text')
    arabic_texts = []
    for elem in arabic_elements:
        text = await elem.inner_text()
        if text.strip():
            arabic_texts.append(text.strip())
    
    # استخراج متن ترجمه - روش اصلی
    translate_elements = await first_container.query_selector_all('.translate-text p, .translate-text')
    translate_texts = []
    for elem in translate_elements:
        text = await elem.inner_text()
        if text.strip():
            translate_texts.append(text.strip())
    
    # اگر ترجمه یافت نشد، جستجو برای p های بدون کلاس که شامل "امام" هستند
    if not translate_texts:
        phrase_container = await first_container.query_selector('.phrase-text-container')
        if phrase_container:
            all_p_elements = await phrase_container.query_selector_all('p')
            for elem in all_p_elements:
                class_attr = await elem.get_attribute('class')
                text = await elem.inner_text()
                # اگر p بدون کلاس است و شامل "امام" و "عليه السلام" است
                if (not class_attr or class_attr == 'None') and text.strip():
                    if 'امام' in text and 'عليه السلام' in text:
                        translate_texts.append(text.strip())
    
    return {
        'arabic_text': '\n'.join(arabic_texts) if arabic_texts else '',
        'translate_text': '\n'.join(translate_texts) if translate_texts else ''
    }

async def test_speech_723_final():
    """تست نهایی خطبه 723"""
    
    url = "http://nahj.makarem.ir/speech/723"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            print("🧪 تست نهایی خطبه 723...")
            
            result = await extract_first_container_improved(page, url)
            
            # بررسی‌های اساسی
            assert result is not None, "کانتینر اول نباید None باشد"
            assert result['arabic_text'], "متن عربی نباید خالی باشد"
            assert result['translate_text'], "متن ترجمه نباید خالی باشد"
            assert 'کلام' in result['arabic_text'], "متن عربی باید شامل 'کلام' باشد"
            assert 'امام' in result['translate_text'], "ترجمه باید شامل 'امام' باشد"
            
            print("✅ همه تست‌ها موفق!")
            print(f"📊 نتایج:")
            print(f"   عربی: {len(result['arabic_text'])} کاراکتر")
            print(f"   ترجمه: {len(result['translate_text'])} کاراکتر")
            print(f"   عربی: {result['arabic_text'][:50]}...")
            print(f"   ترجمه: {result['translate_text'][:50]}...")
            
            return True
            
        except AssertionError as e:
            print(f"❌ تست fail شد: {e}")
            return False
        except Exception as e:
            print(f"❌ خطای غیرمنتظره: {e}")
            return False
        finally:
            await browser.close()

if __name__ == "__main__":
    result = asyncio.run(test_speech_723_final())
    if result:
        print("\n🎯 کد آماده برای اعمال در اسکریپت اصلی است!")
    else:
        print("\n⚠️ نیاز به بررسی بیشتر دارد.")
