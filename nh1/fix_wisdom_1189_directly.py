#!/usr/bin/env python3
"""
رفع مستقیم حکمت 1189
"""

import asyncio
import json
import logging
from playwright.async_api import async_playwright
import re

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_wisdom_1189_directly():
    """رفع مستقیم حکمت 1189"""
    
    # بارگذاری فایل موجود
    try:
        with open('nahj_wisdoms.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        logger.error(f"خطا در بارگذاری فایل: {e}")
        return
    
    # پیدا کردن حکمت 1189
    wisdom_1189 = None
    wisdom_index = -1
    for i, wisdom in enumerate(data['wisdoms']):
        if wisdom['number'] == 1189:
            wisdom_1189 = wisdom
            wisdom_index = i
            break
    
    if not wisdom_1189:
        logger.error("حکمت 1189 یافت نشد!")
        return
    
    logger.info(f"حکمت 1189 یافت شد: {wisdom_1189['title']}")
    logger.info(f"URL: {wisdom_1189['url']}")
    logger.info(f"وضعیت فعلی: {len(wisdom_1189['containers'])} کانتینر")
    
    # استخراج محتوا
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            await page.goto(wisdom_1189['url'], wait_until='domcontentloaded')
            await page.wait_for_timeout(2000)  # انتظار بیشتر
            
            arabic_text = ""
            translate_text = ""
            
            # جستجو برای div با کلاس col-lg-7 col-sm-11 که شامل متن اصلی است
            main_content_div = await page.query_selector('.col-lg-7.col-sm-11')
            
            if main_content_div:
                logger.info("div اصلی یافت شد")
                
                # استخراج متن عربی از p با کلاس arabic-text
                arabic_element = await main_content_div.query_selector('p.arabic-text')
                if arabic_element:
                    logger.info("element عربی یافت شد")
                    # استخراج HTML برای حفظ ساختار
                    arabic_html = await arabic_element.inner_html()
                    logger.debug(f"HTML عربی: {arabic_html[:200]}...")
                    
                    # تمیز کردن HTML و تبدیل <br> به \n
                    arabic_text = arabic_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    # حذف سایر تگ‌های HTML
                    arabic_text = re.sub(r'<[^>]+>', '', arabic_text)
                    # تمیز کردن فضاهای اضافی
                    arabic_text = '\n'.join(line.strip() for line in arabic_text.split('\n') if line.strip())
                    logger.info(f"متن عربی استخراج شد: {len(arabic_text)} کاراکتر")
                else:
                    logger.warning("element عربی یافت نشد")
                    
                # استخراج متن ترجمه از p با کلاس translate-text
                translate_element = await main_content_div.query_selector('p.translate-text')
                if translate_element:
                    logger.info("element ترجمه یافت شد")
                    # استخراج HTML برای حفظ ساختار
                    translate_html = await translate_element.inner_html()
                    logger.debug(f"HTML ترجمه: {translate_html[:200]}...")
                    
                    # تمیز کردن HTML و تبدیل <br> به \n
                    translate_text = translate_html.replace('<br>', '\n').replace('<br/>', '\n').replace('<br />', '\n')
                    # حذف لینک‌های پاورقی
                    translate_text = re.sub(r'<a[^>]*>.*?</a>', '', translate_text)
                    # حذف سایر تگ‌های HTML
                    translate_text = re.sub(r'<[^>]+>', '', translate_text)
                    # تمیز کردن فضاهای اضافی
                    translate_text = '\n'.join(line.strip() for line in translate_text.split('\n') if line.strip())
                    logger.info(f"متن ترجمه استخراج شد: {len(translate_text)} کاراکتر")
                else:
                    logger.warning("element ترجمه یافت نشد")
            else:
                logger.warning("div اصلی یافت نشد")
            
            if arabic_text or translate_text:
                # به‌روزرسانی داده
                container_data = {
                    "number": 1,
                    "header": "",
                    "arabic-text": {"main": arabic_text, "metadata": None},
                    "translate-text": {"main": translate_text, "metadata": None}
                }
                
                # به‌روزرسانی حکمت در داده
                data['wisdoms'][wisdom_index]['containers'] = [container_data]
                data['wisdoms'][wisdom_index]['total_section_phrase_container'] = 1
                
                # ذخیره فایل
                with open('nahj_wisdoms.json', 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"✅ حکمت 1189 با موفقیت رفع شد!")
                logger.info(f"متن عربی: {len(arabic_text)} کاراکتر")
                logger.info(f"متن ترجمه: {len(translate_text)} کاراکتر")
                
                # نمایش نمونه محتوا
                print("\n" + "="*50)
                print("محتوای استخراج شده:")
                print("="*50)
                
                print(f"\nمتن عربی ({len(arabic_text)} کاراکتر):")
                print("-" * 30)
                print(arabic_text)
                
                print(f"\nمتن ترجمه ({len(translate_text)} کاراکتر):")
                print("-" * 30)
                print(translate_text)
                
            else:
                logger.error("❌ نتوانستیم محتوای حکمت 1189 را استخراج کنیم")
                
        except Exception as e:
            logger.error(f"خطا در استخراج: {e}")
            import traceback
            logger.error(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(fix_wisdom_1189_directly())
