#!/usr/bin/env python3
"""
تست ساده برای خطبه 387
"""

import asyncio
from playwright.async_api import async_playwright

async def test_simple_387():
    """تست ساده خطبه 387"""
    
    url = "http://nahj.makarem.ir/speech/387"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(2000)
            
            # گرفتن کانتینر اول
            first_container = await page.query_selector('.container-fluid article')
            phrase_container = await first_container.query_selector('.phrase-text-container')
            
            print("🔍 بررسی ساختار HTML:")
            
            # بررسی translate-text
            translate_element = await phrase_container.query_selector('p.translate-text')
            if translate_element:
                translate_text = await translate_element.inner_text()
                print(f"translate-text: '{translate_text}' (خالی: {not translate_text.strip()})")
            
            # بررسی تمام p ها
            all_p_elements = await phrase_container.query_selector_all('p')
            print(f"\nتمام عناصر p ({len(all_p_elements)} عدد):")
            
            translate_found = False
            for i, elem in enumerate(all_p_elements, 1):
                class_attr = await elem.get_attribute('class')
                text = await elem.inner_text()
                print(f"  p{i}: class='{class_attr}', text='{text[:30]}...'")
                
                # اگر به translate-text رسیدیم
                if class_attr == 'translate-text':
                    translate_found = True
                    print(f"    🎯 translate-text یافت شد (خالی: {not text.strip()})")
                    continue
                
                # اگر بعد از translate-text هستیم و p بدون کلاس است
                if translate_found and (not class_attr or class_attr == 'None'):
                    if text.strip():
                        print(f"    ✅ این احتمالاً متن ترجمه است!")
                        print(f"    متن کامل: {text}")
                        break
            
        except Exception as e:
            print(f"خطا: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_simple_387())
