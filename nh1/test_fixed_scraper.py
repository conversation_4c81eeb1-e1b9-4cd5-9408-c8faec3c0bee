#!/usr/bin/env python3
"""
تست اسکریپت اصلاح شده برای چند حکمت مشکل‌دار
"""

import asyncio
import json
import logging
from nahj_scraper import NahjScraper

# تنظیم لاگ
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_fixed_scraper():
    """تست اسکریپت اصلاح شده"""
    
    # حکمت‌های مشکل‌دار برای تست
    test_wisdoms = [
        {"number": 1189, "url": "http://nahj.makarem.ir/wisdom/1189", "title": "حکمت 224 - سود انفاق"},
        {"number": 1190, "url": "http://nahj.makarem.ir/wisdom/1190", "title": "حکمت 225 - زد و خورد با دشمن"},
        {"number": 1014, "url": "http://nahj.makarem.ir/wisdom/1014", "title": "حکمت 55 - زيان دارايي"}
    ]
    
    scraper = NahjScraper("wisdom")
    
    # شبیه‌سازی محیط اجرا
    from playwright.async_api import async_playwright
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        # غیرفعال کردن تصاویر و CSS برای سرعت بیشتر
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        success_count = 0
        
        for wisdom in test_wisdoms:
            logger.info(f"تست حکمت {wisdom['number']}: {wisdom['title']}")
            
            try:
                # استفاده از متد extract_content از اسکریپت اصلی
                result = await scraper.extract_content(page, wisdom)
                
                if result['containers'] and len(result['containers']) > 0:
                    container = result['containers'][0]
                    arabic_text = container.get('arabic-text', {}).get('main', '')
                    translate_text = container.get('translate-text', {}).get('main', '')
                    
                    if arabic_text or translate_text:
                        logger.info(f"✅ حکمت {wisdom['number']} موفق - عربی: {len(arabic_text)} کاراکتر، ترجمه: {len(translate_text)} کاراکتر")
                        success_count += 1
                    else:
                        logger.warning(f"❌ حکمت {wisdom['number']} خالی")
                else:
                    logger.warning(f"❌ حکمت {wisdom['number']} بدون کانتینر")
                    
            except Exception as e:
                logger.error(f"❌ خطا در حکمت {wisdom['number']}: {e}")
        
        await browser.close()
        
        logger.info(f"نتیجه تست: {success_count}/{len(test_wisdoms)} حکمت موفق")
        
        if success_count == len(test_wisdoms):
            print("🎉 تمام تست‌ها موفق بودند! کد اصلاح شده آماده است.")
        else:
            print(f"⚠️ {len(test_wisdoms) - success_count} تست ناموفق. نیاز به بررسی بیشتر.")

if __name__ == "__main__":
    asyncio.run(test_fixed_scraper())
