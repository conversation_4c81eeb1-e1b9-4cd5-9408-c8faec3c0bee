#!/usr/bin/env python3
"""
دیباگ ساختار HTML حکمت 1434
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_wisdom_1434_html():
    """دیباگ ساختار HTML حکمت 1434"""
    
    url = "http://nahj.makarem.ir/wisdom/1434"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        await page.route("**/*.{png,jpg,jpeg,gif,svg,css,woff,woff2}", lambda route: route.abort())
        
        try:
            await page.goto(url, wait_until='networkidle')
            await page.wait_for_timeout(3000)
            
            print("🔍 بررسی ساختار HTML صفحه:")
            print("="*60)
            
            # بررسی div های اصلی
            main_divs = await page.query_selector_all('.col-lg-7')
            print(f"تعداد div های col-lg-7: {len(main_divs)}")
            
            col_sm_divs = await page.query_selector_all('.col-sm-11')
            print(f"تعداد div های col-sm-11: {len(col_sm_divs)}")
            
            combined_divs = await page.query_selector_all('.col-lg-7.col-sm-11')
            print(f"تعداد div های col-lg-7.col-sm-11: {len(combined_divs)}")
            
            # بررسی تمام div ها
            all_divs = await page.query_selector_all('div')
            print(f"تعداد کل div ها: {len(all_divs)}")
            
            # بررسی div هایی که شامل کلاس col هستند
            col_divs = await page.query_selector_all('div[class*="col"]')
            print(f"تعداد div های شامل col: {len(col_divs)}")
            
            for i, div in enumerate(col_divs[:10]):  # نمایش 10 تای اول
                class_attr = await div.get_attribute('class')
                print(f"  div {i+1}: class='{class_attr}'")
            
            print("\n" + "="*60)
            print("بررسی عناصر p:")
            print("="*60)
            
            # بررسی تمام p ها
            all_p = await page.query_selector_all('p')
            print(f"تعداد کل p ها: {len(all_p)}")
            
            # بررسی p هایی که شامل کلاس arabic یا translate هستند
            arabic_p = await page.query_selector_all('p[class*="arabic"]')
            translate_p = await page.query_selector_all('p[class*="translate"]')
            
            print(f"تعداد p های شامل arabic: {len(arabic_p)}")
            print(f"تعداد p های شامل translate: {len(translate_p)}")
            
            # نمایش تمام p ها با کلاس
            for i, p in enumerate(all_p[:15]):  # نمایش 15 تای اول
                class_attr = await p.get_attribute('class')
                text = await p.inner_text()
                if class_attr or len(text) > 10:
                    print(f"  p {i+1}: class='{class_attr}', text='{text[:50]}...'")
            
            print("\n" + "="*60)
            print("بررسی محتوای کامل صفحه:")
            print("="*60)
            
            # گرفتن محتوای کامل صفحه
            page_content = await page.content()
            
            # جستجو برای کلمات کلیدی
            if 'arabic-text' in page_content:
                print("✅ 'arabic-text' در صفحه یافت شد")
            else:
                print("❌ 'arabic-text' در صفحه یافت نشد")
                
            if 'translate-text' in page_content:
                print("✅ 'translate-text' در صفحه یافت شد")
            else:
                print("❌ 'translate-text' در صفحه یافت نشد")
                
            if 'قَالَ عَلَيهِ السَّلَام' in page_content:
                print("✅ متن عربی در صفحه یافت شد")
            else:
                print("❌ متن عربی در صفحه یافت نشد")
                
            if 'امام (عليه السلام)' in page_content:
                print("✅ متن ترجمه در صفحه یافت شد")
            else:
                print("❌ متن ترجمه در صفحه یافت نشد")
            
            # نمایش بخشی از HTML که شامل محتوای مورد نظر است
            if 'قَالَ عَلَيهِ السَّلَام' in page_content:
                start_index = page_content.find('قَالَ عَلَيهِ السَّلَام') - 200
                end_index = start_index + 800
                relevant_html = page_content[max(0, start_index):end_index]
                print(f"\nHTML مربوطه:")
                print("-" * 40)
                print(relevant_html)
                print("-" * 40)
            
        except Exception as e:
            print(f"خطا: {e}")
            import traceback
            print(f"جزئیات خطا: {traceback.format_exc()}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_wisdom_1434_html())
