#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بررسی دقیق محتوای masael برای اصلاح الگوریتم
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_masael_page():
    """تحلیل دقیق صفحه Vacib namazlar"""
    print("🔍 تحلیل دقیق صفحه 'Vacib namazlar'")
    
    url = "https://www.leader.ir/az/book/246/1?sn=31659"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # 1. بررسی عنوان صفحه
        title_element = soup.find('title')
        if title_element:
            print(f"📄 عنوان صفحه: {title_element.get_text().strip()}")
        
        # 2. جستجوی عناصر strong که شامل "Məsələ" هستند
        print("\n🔍 جستجوی عناصر strong:")
        strong_elements = soup.find_all('strong')
        masael_strongs = []
        
        for i, strong in enumerate(strong_elements):
            text = strong.get_text().strip()
            if re.match(r'Məsələ\s+\d+\.', text, re.IGNORECASE):
                masael_strongs.append((i, strong, text))
                print(f"  {i+1}. {text}")
        
        print(f"\n📊 تعداد عناصر strong با 'Məsələ': {len(masael_strongs)}")
        
        # 3. تحلیل اولین مسئله
        if masael_strongs:
            print(f"\n🔍 تحلیل اولین مسئله:")
            first_masael = masael_strongs[0]
            strong_element = first_masael[1]
            
            print(f"Header: {first_masael[2]}")
            
            # پیدا کردن متن بعد از strong
            parent = strong_element.parent
            if parent:
                print(f"Parent tag: {parent.name}")
                
                # گرفتن تمام متن پاراگراف
                full_text = parent.get_text()
                print(f"Full paragraph text: {full_text[:200]}...")
                
                # حذف header از متن
                text_after_header = full_text.replace(first_masael[2], '', 1).strip()
                print(f"Text after header: {text_after_header[:200]}...")
        
        # 4. بررسی ساختار کلی صفحه
        print(f"\n🔍 ساختار کلی صفحه:")
        
        # جستجوی div های اصلی
        main_divs = soup.find_all('div', class_=True)
        print(f"تعداد div های دارای class: {len(main_divs)}")
        
        # جستجوی p تگ‌ها
        paragraphs = soup.find_all('p')
        print(f"تعداد پاراگراف‌ها: {len(paragraphs)}")
        
        # نمایش چند پاراگراف اول
        print(f"\nچند پاراگراف اول:")
        for i, p in enumerate(paragraphs[:5]):
            text = p.get_text().strip()
            if text:
                print(f"  P{i+1}: {text[:100]}...")
        
        # 5. جستجوی الگوی دقیق‌تر
        print(f"\n🔍 جستجوی الگوی دقیق‌تر:")
        
        # جستجوی تمام متن صفحه
        page_text = soup.get_text()
        
        # پیدا کردن اولین مسئله با regex
        first_masael_match = re.search(r'(Məsələ\s+1\.)\s*(.*?)(?=Məsələ\s+2\.|$)', page_text, re.DOTALL | re.IGNORECASE)
        
        if first_masael_match:
            header = first_masael_match.group(1)
            content = first_masael_match.group(2).strip()
            
            print(f"Header (regex): {header}")
            print(f"Content (regex): {content[:200]}...")
        
        return soup
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return None


def analyze_specific_masael():
    """تحلیل مسئله خاص"""
    print("\n" + "="*50)
    print("🔍 تحلیل مسئله خاص")
    
    # تست با صفحه‌ای که فقط یک مسئله دارد
    url = "https://www.leader.ir/az/book/246/1?sn=31664"  # Namazların ardıcıllığı
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # جستجوی عناصر strong
        strong_elements = soup.find_all('strong')
        masael_count = 0
        
        for strong in strong_elements:
            text = strong.get_text().strip()
            if re.match(r'Məsələ\s+\d+\.', text, re.IGNORECASE):
                masael_count += 1
                print(f"  مسئله یافت شده: {text}")
                
                # بررسی محتوای بعدی
                parent = strong.parent
                if parent:
                    full_text = parent.get_text()
                    content = full_text.replace(text, '', 1).strip()
                    print(f"  محتوا: {content[:150]}...")
        
        print(f"📊 تعداد مسائل در این صفحه: {masael_count}")
        
    except Exception as e:
        print(f"❌ خطا: {e}")


if __name__ == "__main__":
    analyze_masael_page()
    analyze_specific_masael()
