#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست ساختار اصلاح شده "Namazda örtünmə"
"""

from crawler import LeaderCrawler


def test_fixed_structure():
    """تست ساختار اصلاح شده"""
    print("🚀 تست ساختار اصلاح شده 'Namazda örtünmə'")
    print("=" * 60)
    
    crawler = LeaderCrawler()
    
    # شبیه‌سازی آیتم "Namazda örtünmə" به عنوان sub_category
    namazda_ortunme_item = {
        'id': '31668',
        'title': 'Namazda örtünmə',
        'type': 'sub_category',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31668',
        'children': []
    }
    
    print(f"📂 آیتم اصلی: {namazda_ortunme_item['title']} (نوع: {namazda_ortunme_item['type']})")
    
    # استخراج زیرمجموعه‌ها با روش جدید
    print(f"\n🔍 استخراج زیرمجموعه‌ها با روش جدید...")
    try:
        crawler.extract_deep_subcategories(namazda_ortunme_item)
        
        print(f"✅ استخراج موفق!")
        print(f"📊 تعداد زیرمجموعه‌ها: {len(namazda_ortunme_item['children'])}")
        
        # نمایش زیرمجموعه‌ها
        for i, child in enumerate(namazda_ortunme_item['children'], 1):
            print(f"  {i}. {child['title']} (نوع: {child['type']}, ID: {child['id']})")
            
            # اگر masael است، محتوا را استخراج کن
            if child['type'] == 'masael':
                print(f"    📖 استخراج محتوای masael...")
                crawler.extract_masael_content(child)
                
                if child.get('masael_contents'):
                    print(f"    ✅ {len(child['masael_contents'])} مسئله استخراج شد")
                    
                    # نمایش چند مسئله اول
                    for j, masael in enumerate(child['masael_contents'][:3], 1):
                        print(f"      {j}. {masael['header']} - {masael['text'][:50]}...")
                else:
                    print(f"    ❌ هیچ مسئله‌ای استخراج نشد")
            
            # اگر sub_category است، زیرمجموعه‌هایش را نیز بررسی کن
            elif child['type'] == 'sub_category':
                print(f"    📂 استخراج زیرمجموعه‌های {child['title']}...")
                crawler.extract_deep_subcategories(child, depth=1)
                
                if child.get('children'):
                    print(f"    📊 {len(child['children'])} زیرمجموعه یافت شد")
                    
                    for k, grandchild in enumerate(child['children'][:3], 1):  # فقط 3 مورد اول
                        print(f"      {k}. {grandchild['title']} (نوع: {grandchild['type']})")
                        
                        # اگر masael است، محتوا را استخراج کن
                        if grandchild['type'] == 'masael':
                            print(f"        📖 استخراج محتوای masael...")
                            crawler.extract_masael_content(grandchild)
                            
                            if grandchild.get('masael_contents'):
                                print(f"        ✅ {len(grandchild['masael_contents'])} مسئله استخراج شد")
                                
                                # نمایش مسئله اول
                                if grandchild['masael_contents']:
                                    first = grandchild['masael_contents'][0]
                                    print(f"          📝 {first['header']} - {first['text'][:50]}...")
                            else:
                                print(f"        ❌ هیچ مسئله‌ای استخراج نشد")
                else:
                    print(f"    ❌ هیچ زیرمجموعه‌ای یافت نشد")
        
        # خلاصه نهایی
        print(f"\n🎯 خلاصه ساختار نهایی:")
        print_structure(namazda_ortunme_item, 0)
        
        # بررسی اینکه آیا مسائل 48-56 یافت شدند
        found_48_56 = False
        for child in namazda_ortunme_item['children']:
            if child['title'] == 'Namazda örtünmə' and child.get('masael_contents'):
                for masael in child['masael_contents']:
                    if 'Məsələ 48' in masael['header']:
                        found_48_56 = True
                        break
        
        if found_48_56:
            print(f"\n🎉 عالی! مسائل 48-56 با موفقیت یافت شدند!")
        else:
            print(f"\n⚠️ مسائل 48-56 یافت نشدند")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return False


def print_structure(item, depth=0):
    """نمایش ساختار سلسله مراتبی"""
    indent = "  " * depth
    icon = "📂" if item['type'] == 'sub_category' else "📄"
    
    masael_count = len(item.get('masael_contents', []))
    masael_info = f" ({masael_count} مسئله)" if masael_count > 0 else ""
    
    print(f"{indent}{icon} {item['title']} ({item['type']}){masael_info}")
    
    if item.get('children'):
        for child in item['children'][:5]:  # فقط 5 مورد اول
            print_structure(child, depth + 1)


if __name__ == "__main__":
    success = test_fixed_structure()
    if success:
        print(f"\n🎯 تست موفق! ساختار اصلاح شده کار می‌کند.")
    else:
        print(f"\n💥 تست ناموفق!")
