#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست اصلاح sub_category ها
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def test_sub_category_fix():
    """تست اصلاح sub_category ها"""
    print("🚀 تست اصلاح sub_category ها")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # تست "Namazda örtünmə" به عنوان sub_category
    test_item = {
        'id': '31668',
        'title': 'Namazda örtünmə',
        'type': 'sub_category',  # نوع sub_category
        'url': 'https://www.leader.ir/az/book/246/1?sn=31668',
        'masael_contents': []
    }
    
    print(f"🔍 تست استخراج محتوا برای: {test_item['title']} (نوع: {test_item['type']})")
    
    # استخراج محتوا
    crawler.extract_masael_content(test_item)
    
    # بررسی نتایج
    if test_item.get('masael_contents'):
        contents = test_item['masael_contents']
        print(f"\n✅ نتایج:")
        print(f"📊 تعداد مسائل استخراج شده: {len(contents)}")
        
        # بررسی اینکه آیا مسائل 48-56 استخراج شده‌اند
        extracted_numbers = []
        for masael in contents:
            import re
            number_match = re.search(r'Məsələ\s+(\d+)\.', masael['header'])
            if number_match:
                extracted_numbers.append(int(number_match.group(1)))
        
        expected_numbers = list(range(48, 57))  # 48 تا 56
        
        print(f"📋 مسائل انتظار: {expected_numbers}")
        print(f"📋 مسائل استخراج شده: {sorted(extracted_numbers)}")
        
        if any(n in expected_numbers for n in extracted_numbers):
            print("✅ برخی از مسائل مورد انتظار استخراج شد")
            
            # نمایش چند نمونه
            print(f"\n📝 چند نمونه:")
            for i, masael in enumerate(contents[:3], 1):
                print(f"{i}. {masael['header']}")
                print(f"   {masael['text'][:80]}...")
            
            return True
        else:
            print("❌ مسائل مورد انتظار استخراج نشد")
            print("📝 مسائل استخراج شده:")
            for i, masael in enumerate(contents[:5], 1):
                print(f"{i}. {masael['header']}")
                print(f"   {masael['text'][:80]}...")
            return False
    
    else:
        print("❌ هیچ محتوایی استخراج نشد!")
        return False


if __name__ == "__main__":
    success = test_sub_category_fix()
    if success:
        print(f"\n🎉 تست موفق! sub_category ها حالا به درستی پردازش می‌شوند")
    else:
        print(f"\n💥 تست ناموفق! مشکل هنوز وجود دارد")
