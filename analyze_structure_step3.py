#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرحله 3: بررسی "Namaz paltarının şərtləri"
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_namaz_paltarinin_sertleri():
    """تحلیل Namaz paltarının şərtləri"""
    print("🔍 مرحله 3: بررسی 'Namaz paltarının şərtləri'")
    print("=" * 60)
    
    url = "https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31669"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # 1. بررسی عنوان صفحه
        title_element = soup.find('title')
        if title_element:
            print(f"📄 عنوان صفحه: {title_element.get_text().strip()}")
        
        # 2. بررسی breadcrumb (مسیر ناوبری)
        breadcrumb = soup.find('label')
        if breadcrumb:
            print(f"🗂️ مسیر ناوبری: {breadcrumb.get_text().strip()}")
        
        # 3. بررسی محتوای مسائل
        print(f"\n📝 بررسی محتوای مسائل:")
        page_text = soup.get_text()
        
        # جستجوی مسئله 57
        if "Məsələ 57" in page_text:
            print(f"  ✅ مسئله 57 یافت شد!")
            
            # استخراج محتوای مسئله 57
            pattern_57 = r'(Məsələ\s+57\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
            match_57 = re.search(pattern_57, page_text, re.DOTALL | re.IGNORECASE)
            if match_57:
                header, text = match_57.groups()
                clean_text = text.strip()
                clean_text = re.sub(r'\s+', ' ', clean_text)
                print(f"  📖 {header}")
                print(f"     {clean_text[:200]}...")
        else:
            print(f"  ❌ مسئله 57 یافت نشد")
        
        # جستجوی تمام مسائل موجود
        masael_pattern = re.compile(r'Məsələ\s+(\d+)\.', re.IGNORECASE)
        matches = masael_pattern.findall(page_text)
        
        if matches:
            numbers = [int(m) for m in matches]
            unique_numbers = sorted(set(numbers))
            print(f"  📋 تمام مسائل یافت شده: {unique_numbers}")
        else:
            print(f"  ❌ هیچ مسئله‌ای یافت نشد")
        
        # 4. جستجوی لینک‌های زیرمجموعه
        print(f"\n🔗 جستجوی لینک‌های زیرمجموعه:")
        links = soup.find_all('a', href=True)
        sub_links = []
        
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            
            if 'sn=' in href and text and len(text) > 5:
                # استخراج sn
                sn_match = re.search(r'sn=(\d+)', href)
                if sn_match:
                    sn = sn_match.group(1)
                    sub_links.append((text, sn, href))
        
        # فیلتر کردن لینک‌های مرتبط
        relevant_links = []
        for text, sn, href in sub_links:
            if any(keyword in text.lower() for keyword in ['paltar', 'şərt', 'namaz']):
                relevant_links.append((text, sn, href))
                print(f"  ✅ {text} (sn={sn})")
        
        return relevant_links
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return []


if __name__ == "__main__":
    links = analyze_namaz_paltarinin_sertleri()
    print(f"\n🎯 خلاصه: {len(links)} زیرمجموعه مرتبط یافت شد")
    for text, sn, href in links:
        print(f"  - {text} (sn={sn})")
