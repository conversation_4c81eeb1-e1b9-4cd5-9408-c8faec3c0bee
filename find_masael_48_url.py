#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
پیدا کردن URL مسائل 48-56
"""

import requests
import re
from bs4 import BeautifulSoup


def find_masael_48_url():
    """پیدا کردن URL مسائل 48-56"""
    print("🔍 جستجوی URL مسائل 48-56")
    print("=" * 50)
    
    # بررسی مستقیم URL مسئله 48
    direct_url = "https://www.leader.ir/az/book/246/1?sn=31668"
    
    try:
        response = requests.get(direct_url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # جستجوی تمام مسائل موجود در صفحه
        print(f"🔍 تمام مسائل موجود در صفحه:")
        page_text = soup.get_text()
        
        # پیدا کردن تمام مسائل
        masael_pattern = re.compile(r'Məsələ\s+(\d+)\.', re.IGNORECASE)
        matches = masael_pattern.findall(page_text)
        
        if matches:
            numbers = [int(m) for m in matches]
            unique_numbers = sorted(set(numbers))
            print(f"📋 مسائل یافت شده: {unique_numbers}")
            
            # بررسی اینکه آیا 48 در میان آنها هست
            if 48 in unique_numbers:
                print("✅ مسئله 48 یافت شد!")
                
                # استخراج محتوای مسئله 48
                pattern_48 = r'(Məsələ\s+48\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                match_48 = re.search(pattern_48, page_text, re.DOTALL | re.IGNORECASE)
                if match_48:
                    header, text = match_48.groups()
                    clean_text = text.strip()
                    clean_text = re.sub(r'\s+', ' ', clean_text)
                    print(f"📝 {header}")
                    print(f"   {clean_text[:200]}...")
            else:
                print("❌ مسئله 48 یافت نشد")
                
                # نمایش چند مسئله اول
                print(f"📝 چند مسئله اول:")
                for i, num in enumerate(unique_numbers[:10], 1):
                    pattern = rf'(Məsələ\s+{num}\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                    match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                    if match:
                        header, text = match.groups()
                        clean_text = text.strip()
                        clean_text = re.sub(r'\s+', ' ', clean_text)
                        print(f"  {i}. {header}")
                        print(f"     {clean_text[:80]}...")
        else:
            print("❌ هیچ مسئله‌ای یافت نشد")
            
        # بررسی اینکه آیا متن "Məsələ 48" در صفحه وجود دارد
        if "Məsələ 48" in page_text:
            print("✅ متن 'Məsələ 48' در صفحه یافت شد")
            # پیدا کردن موقعیت آن
            index = page_text.find("Məsələ 48")
            context = page_text[max(0, index-100):index+200]
            print(f"📍 متن اطراف: ...{context}...")
        else:
            print("❌ متن 'Məsələ 48' در صفحه یافت نشد")
            
    except Exception as e:
        print(f"❌ خطا: {e}")


if __name__ == "__main__":
    find_masael_48_url()
