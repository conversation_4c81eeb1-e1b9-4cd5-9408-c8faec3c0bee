#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست ساده کراولر
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from datetime import datetime


def test_simple():
    """تست ساده"""
    print("🚀 تست ساده کراولر")
    
    url = "https://www.leader.ir/az/book/246/1?sn=31668"  # Namazda örtünmə
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # جستجوی لینک‌های زیرمجموعه
        sub_links = soup.find_all('a', href=re.compile(r'1\?sn=\d+'))
        
        print(f"📊 تعداد لینک‌های یافت شده: {len(sub_links)}")
        
        for i, link in enumerate(sub_links[:10]):  # نمایش 10 مورد اول
            href = link.get('href')
            title = link.get('title', '').strip()
            
            if title and href:
                sn_match = re.search(r'sn=(\d+)', href)
                sn = sn_match.group(1) if sn_match else 'N/A'
                print(f"  {i+1}. {title} (sn={sn})")
        
    except Exception as e:
        print(f"❌ خطا: {e}")


if __name__ == "__main__":
    test_simple()
