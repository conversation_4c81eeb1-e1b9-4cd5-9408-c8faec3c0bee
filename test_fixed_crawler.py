#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست کراولر اصلاح شده
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def main():
    """تست کراولر اصلاح شده"""
    print("🚀 تست کراولر اصلاح شده")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # مرحله 1: استخراج دسته‌بندی‌های اصلی
    main_categories = crawler.extract_main_categories()
    
    if not main_categories:
        print("❌ هیچ دسته‌بندی اصلی یافت نشد!")
        return
    
    print(f"✅ {len(main_categories)} دسته‌بندی اصلی یافت شد")
    
    # مرحله 2: استخراج زیرمجموعه‌ها (فقط NAMAZ برای تست)
    namaz_category = None
    for cat in main_categories:
        if cat['title'] == 'NAMAZ':
            namaz_category = cat
            break
    
    if namaz_category:
        print(f"\n🔍 تست با دسته‌بندی: {namaz_category['title']}")
        crawler.extract_subcategories(namaz_category)
        print(f"✅ {len(namaz_category['children'])} زیرمجموعه یافت شد")
        
        # مرحله 3: تست زیرمجموعه‌های عمیق (فقط 2 مورد اول)
        sub_category_count = 0
        for item in namaz_category['children']:
            if item['type'] == 'sub_category' and sub_category_count < 2:
                print(f"\n🔍 تست زیرمجموعه: {item['title']}")
                crawler.extract_deep_subcategories(item)
                sub_category_count += 1
        
        # مرحله 4: تست استخراج محتوا (فقط 1 masael)
        masael_count = 0
        for item in namaz_category['children']:
            if item['type'] == 'masael' and masael_count < 1:
                print(f"\n📖 تست استخراج محتوا: {item['title']}")
                crawler.extract_masael_content(item)
                if item.get('masael_contents'):
                    print(f"✅ {len(item['masael_contents'])} مسئله استخراج شد")
                masael_count += 1
    
    print(f"\n📊 آمار cache: {len(crawler.page_cache)} صفحه")
    print(f"📊 آمار پردازش: {len(crawler.processed_items)} آیتم")
    print("\n✅ تست تکمیل شد!")


if __name__ == "__main__":
    main()
