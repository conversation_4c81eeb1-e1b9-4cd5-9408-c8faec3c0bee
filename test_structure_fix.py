#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست اصلاحات ساختار "Namazda örtünmə"
"""

from crawler import LeaderCrawler


def test_structure_fix():
    """تست اصلاحات ساختار"""
    print("🚀 تست اصلاحات ساختار 'Namazda örtünmə'")
    print("=" * 60)
    
    crawler = LeaderCrawler()
    
    # تست 1: تشخیص نوع "Namazda örtünmə"
    print("🔍 تست 1: تشخیص نوع 'Namazda örtünmə'")
    item_type = crawler.determine_item_type("Namazda örtünmə", "31668")
    print(f"  نوع تشخیص داده شده: {item_type}")
    if item_type == "sub_category":
        print("  ✅ صحیح! باید sub_category باشد")
    else:
        print("  ❌ اشتباه! باید sub_category باشد")
    
    # تست 2: تشخیص نوع "Namaz paltarının şərtləri"
    print("\n🔍 تست 2: تشخیص نوع 'Namaz paltarının şərtləri'")
    item_type = crawler.determine_item_type("Namaz paltarının şərtləri", "31669")
    print(f"  نوع تشخیص داده شده: {item_type}")
    if item_type == "sub_category":
        print("  ✅ صحیح! باید sub_category باشد")
    else:
        print("  ❌ اشتباه! باید sub_category باشد")
    
    # تست 3: تشخیص نوع "Namaz paltarına aid müstəhəb və məkruh işlər"
    print("\n🔍 تست 3: تشخیص نوع 'Namaz paltarına aid müstəhəb və məkruh işlər'")
    item_type = crawler.determine_item_type("Namaz paltarına aid müstəhəb və məkruh işlər", "31677")
    print(f"  نوع تشخیص داده شده: {item_type}")
    if item_type == "masael":
        print("  ✅ صحیح! باید masael باشد")
    else:
        print("  ❌ اشتباه! باید masael باشد")
    
    # تست 4: استخراج محتوای HTML ثابت
    print("\n🔍 تست 4: استخراج محتوای HTML ثابت")
    
    # تست Namazda örtünmə
    namazda_ortunme_content = crawler._get_static_html_data("Namazda örtünmə")
    print(f"  📖 Namazda örtünmə: {len(namazda_ortunme_content)} مسئله")
    if len(namazda_ortunme_content) == 9:
        print("    ✅ صحیح! 9 مسئله (48-56)")
        # نمایش اولین مسئله
        if namazda_ortunme_content:
            first = namazda_ortunme_content[0]
            print(f"    📝 نمونه: {first['header']} - {first['text'][:50]}...")
    else:
        print(f"    ❌ اشتباه! باید 9 مسئله باشد، نه {len(namazda_ortunme_content)}")
    
    # تست Namaz paltarının şərtləri
    paltarin_sertleri_content = crawler._get_static_html_data("Namaz paltarının şərtləri")
    print(f"  📖 Namaz paltarının şərtləri: {len(paltarin_sertleri_content)} مسئله")
    if len(paltarin_sertleri_content) == 1:
        print("    ✅ صحیح! 1 مسئله (57)")
        # نمایش مسئله
        if paltarin_sertleri_content:
            first = paltarin_sertleri_content[0]
            print(f"    📝 مسئله: {first['header']} - {first['text'][:50]}...")
    else:
        print(f"    ❌ اشتباه! باید 1 مسئله باشد، نه {len(paltarin_sertleri_content)}")
    
    # تست Namaz paltarına aid müstəhəb və məkruh işlər
    musteheb_mekruh_content = crawler._get_static_html_data("Namaz paltarına aid müstəhəb və məkruh işlər")
    print(f"  📖 Namaz paltarına aid müstəhəb və məkruh işlər: {len(musteheb_mekruh_content)} مسئله")
    if len(musteheb_mekruh_content) == 2:
        print("    ✅ صحیح! 2 مسئله (99-100)")
        # نمایش اولین مسئله
        if musteheb_mekruh_content:
            first = musteheb_mekruh_content[0]
            print(f"    📝 نمونه: {first['header']} - {first['text'][:50]}...")
    else:
        print(f"    ❌ اشتباه! باید 2 مسئله باشد، نه {len(musteheb_mekruh_content)}")
    
    print(f"\n🎯 خلاصه تست:")
    print(f"  📂 Namazda örtünmə: sub_category")
    print(f"    └── 📄 Namazda örtünmə: masael (مسائل 48-56)")
    print(f"    └── 📂 Namaz paltarının şərtləri: sub_category")
    print(f"        └── 📄 Namaz paltarının şərtləri: masael (مسئله 57)")
    print(f"    └── 📄 Namaz paltarına aid müstəhəb və məkruh işlər: masael (مسائل 99-100)")


if __name__ == "__main__":
    test_structure_fix()
