#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جستجوی گسترده مسائل 48 در سایت
"""

import requests
import re
from bs4 import BeautifulSoup


def search_masael_48_extended():
    """جستجوی گسترده مسائل 48"""
    print("🔍 جستجوی گسترده مسائل 48 در سایت")
    print("=" * 50)
    
    # بررسی محدوده گسترده‌تر
    base_url = "https://www.leader.ir/az/book/246/1?sn="
    
    # محدوده گسترده sn ها
    sn_ranges = [
        range(31650, 31680),  # اطراف 31668
        range(31680, 31710),  # بعد از 31668
    ]
    
    for sn_range in sn_ranges:
        print(f"\n🔍 بررسی محدوده {sn_range.start}-{sn_range.stop-1}")
        
        for sn in sn_range:
            url = f"{base_url}{sn}"
            
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    soup = BeautifulSoup(response.content, 'html.parser')
                    page_text = soup.get_text()
                    
                    # جستجوی مسئله 48
                    if "Məsələ 48" in page_text:
                        print(f"\n✅ مسئله 48 در sn={sn} یافت شد!")
                        print(f"🌐 URL: {url}")
                        
                        # استخراج عنوان صفحه
                        title_element = soup.find('title')
                        if title_element:
                            title = title_element.get_text().strip()
                            print(f"📄 عنوان صفحه: {title}")
                        
                        # بررسی مسائل 48-56
                        found_numbers = []
                        for num in range(48, 57):
                            if f"Məsələ {num}" in page_text:
                                found_numbers.append(num)
                        
                        print(f"📋 مسائل 48-56 یافت شده: {found_numbers}")
                        
                        # نمایش محتوای مسئله 48
                        pattern_48 = r'(Məsələ\s+48\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                        match_48 = re.search(pattern_48, page_text, re.DOTALL | re.IGNORECASE)
                        if match_48:
                            header, text = match_48.groups()
                            clean_text = text.strip()
                            clean_text = re.sub(r'\s+', ' ', clean_text)
                            print(f"📝 {header}")
                            print(f"   {clean_text[:200]}...")
                        
                        return sn, url
                    
                    # نمایش پیشرفت هر 10 صفحه
                    if sn % 10 == 0:
                        print(f"  📊 بررسی شد تا sn={sn}")
                        
            except Exception as e:
                if sn % 10 == 0:
                    print(f"  ❌ خطا در sn={sn}: {e}")
                continue
    
    print(f"\n💥 مسئله 48 در محدوده‌های بررسی شده یافت نشد!")
    
    # احتمال اینکه مسائل در صفحه دیگری باشند
    print(f"\n🤔 احتمالات:")
    print(f"1. مسائل 48-56 ممکن است در کتاب دیگری باشند")
    print(f"2. شماره‌گذاری مسائل ممکن است متفاوت باشد")
    print(f"3. مسائل ممکن است در بخش دیگری از همین کتاب باشند")
    
    return None, None


if __name__ == "__main__":
    sn, url = search_masael_48_extended()
    if sn:
        print(f"\n🎯 URL صحیح مسائل 48-56: {url}")
    else:
        print(f"\n❓ نیاز به بررسی بیشتر دارد")
