#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست اصلاح "Namazda örtünmə"
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def test_namazda_ortunme_fix():
    """تست اصلاح Namazda örtünmə"""
    print("🚀 تست اصلاح 'Namazda örtünmə'")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # تست تشخیص نوع
    title = "Namazda örtünmə"
    sn = "31668"
    
    item_type = crawler.determine_item_type(title, sn)
    print(f"🔍 تشخیص نوع برای '{title}' (sn={sn}): {item_type}")
    
    if item_type == "masael":
        print("✅ صحیح! به عنوان masael تشخیص داده شد")
    else:
        print("❌ خطا! هنوز به عنوان sub_category تشخیص داده می‌شود")
        return False
    
    # تست استخراج محتوا
    test_item = {
        'id': '31668',
        'title': 'Namazda örtünmə',
        'type': 'masael',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31668',
        'masael_contents': []
    }
    
    print(f"\n📖 تست استخراج محتوا...")
    crawler.extract_masael_content(test_item)
    
    # بررسی نتایج
    if test_item.get('masael_contents'):
        contents = test_item['masael_contents']
        print(f"\n✅ نتایج:")
        print(f"📊 تعداد مسائل استخراج شده: {len(contents)}")
        
        # بررسی اینکه آیا مسائل 48-56 استخراج شده‌اند
        extracted_numbers = []
        for masael in contents:
            import re
            number_match = re.search(r'Məsələ\s+(\d+)\.', masael['header'])
            if number_match:
                extracted_numbers.append(int(number_match.group(1)))
        
        expected_numbers = list(range(48, 57))  # 48 تا 56
        
        print(f"📋 مسائل انتظار: {expected_numbers}")
        print(f"📋 مسائل استخراج شده: {sorted(extracted_numbers)}")
        
        if sorted(extracted_numbers) == expected_numbers:
            print("✅ عالی! تمام مسائل 48-56 استخراج شد")
            
            # نمایش چند نمونه
            print(f"\n📝 چند نمونه:")
            for i, masael in enumerate(contents[:3], 1):
                print(f"{i}. {masael['header']}")
                print(f"   {masael['text'][:80]}...")
            
            return True
        else:
            print("❌ مسائل استخراج شده با انتظارات مطابقت ندارد")
            # نمایش آنچه استخراج شده
            print("📝 مسائل استخراج شده:")
            for i, masael in enumerate(contents[:5], 1):
                print(f"{i}. {masael['header']}")
                print(f"   {masael['text'][:80]}...")
            return False
    
    else:
        print("❌ هیچ محتوایی استخراج نشد!")
        return False


if __name__ == "__main__":
    success = test_namazda_ortunme_fix()
    if success:
        print(f"\n🎉 تست موفق! مشکل حل شد")
    else:
        print(f"\n💥 تست ناموفق! مشکل هنوز وجود دارد")
