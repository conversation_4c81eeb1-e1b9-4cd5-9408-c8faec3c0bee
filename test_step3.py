#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست مرحله 3 - استخراج زیرمجموعه‌های sub_category
"""

import requests
import json
import time
import re
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse
from datetime import datetime


class TestStep3:
    def __init__(self):
        self.base_url = "https://www.leader.ir/az/book/246/NAMAZ-V%C6%8F-ORUC-R%C4%B0SAL%C6%8FS%C4%B0"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        })
        self.delay = 0.5
        
    def get_page(self, url: str):
        """دریافت صفحه"""
        try:
            print(f"📥 در حال دریافت: {url}")
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            time.sleep(self.delay)
            soup = BeautifulSoup(response.content, 'html.parser')
            print(f"✅ دریافت موفق: {response.status_code}")
            return soup
        except Exception as e:
            print(f"❌ خطا: {e}")
            return None
    
    def determine_item_type(self, title: str, sn: str) -> str:
        """تشخیص نوع آیتم"""
        known_sub_categories = {
            '31668', '31669', '31670', '31671', '31672', '31673', '31674'
        }
        
        if sn in known_sub_categories:
            return 'sub_category'
        
        sub_category_patterns = [
            r'şərtləri$', r'hökmləri$', r'örtünmə$', r'^\d+\)', 
            r'namazı$', r'əməlləri$', r'hallar$'
        ]
        
        for pattern in sub_category_patterns:
            if re.search(pattern, title, re.IGNORECASE):
                return 'sub_category'
        
        return 'masael'
    
    def test_namazda_ortunme(self):
        """تست زیرمجموعه‌های 'Namaz paltarının şərtləri'"""
        print("🔍 تست: Namaz paltarının şərtləri")

        url = "https://www.leader.ir/az/book/246/1?sn=31669"
        soup = self.get_page(url)
        
        if not soup:
            return
        
        # جستجوی لینک‌های زیرمجموعه
        sub_links = soup.find_all('a', href=re.compile(r'1\?sn=\d+'))
        
        results = []
        for link in sub_links:
            href = link.get('href')
            title = link.get('title', '').strip()
            
            if not title or not href:
                continue
            
            sn_match = re.search(r'sn=(\d+)', href)
            if not sn_match:
                continue
                
            sn = sn_match.group(1)
            
            # حذف خود صفحه
            if sn == '31669':
                continue
            
            item_type = self.determine_item_type(title, sn)
            
            results.append({
                'id': sn,
                'title': title,
                'type': item_type,
                'url': urljoin(self.base_url, href)
            })
            
            print(f"  ✅ {item_type}: {title} (sn={sn})")
        
        print(f"📊 تعداد زیرمجموعه‌ها: {len(results)}")
        return results


def main():
    """تابع اصلی"""
    print("🚀 تست مرحله 3")
    print("=" * 50)
    
    tester = TestStep3()
    results = tester.test_namazda_ortunme()
    
    print("\n📋 نتایج:")
    for item in results:
        print(f"  - {item['type']}: {item['title']} (ID: {item['id']})")
    
    print("\n✅ تست تکمیل شد!")


if __name__ == "__main__":
    main()
