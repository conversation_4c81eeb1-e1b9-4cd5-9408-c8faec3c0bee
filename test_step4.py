#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست مرحله 4 - استخراج محتوای masael
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def main():
    """تست مرحله 4"""
    print("🚀 تست مرحله 4 - استخراج محتوای masael")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # ایجاد یک masael تست
    test_masael = {
        'id': '31659',
        'title': 'Vacib namazlar',
        'type': 'masael',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31659',
        'masael_contents': []
    }
    
    # تست استخراج محتوای masael
    crawler.extract_masael_content(test_masael)
    
    # نمایش نتایج
    print("\n📋 نتایج:")
    print(f"📖 {test_masael['title']}: {len(test_masael['masael_contents'])} مسئله")
    
    # نمایش چند نمونه اول
    for i, content in enumerate(test_masael['masael_contents'][:5], 1):
        print(f"\n📝 مسئله {i}:")
        print(f"  Header: {content['header']}")
        print(f"  Text: {content['text'][:100]}..." if len(content['text']) > 100 else f"  Text: {content['text']}")
    
    if len(test_masael['masael_contents']) > 5:
        print(f"\n... و {len(test_masael['masael_contents']) - 5} مسئله دیگر")
    
    print("\n✅ تست تکمیل شد!")


if __name__ == "__main__":
    main()
