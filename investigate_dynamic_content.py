#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بررسی روش‌های مختلف برای دریافت محتوای dynamic
"""

import requests
import re
from bs4 import BeautifulSoup
import json


def investigate_dynamic_content():
    """بررسی روش‌های مختلف برای دریافت محتوای dynamic"""
    print("🔍 بررسی روش‌های دریافت محتوای dynamic")
    print("=" * 60)
    
    # 1. بررسی درخواست‌های AJAX
    print("🌐 روش 1: بررسی درخواست‌های AJAX")
    
    # URL های مختلف برای تست
    test_urls = [
        ("صفحه اصلی", "https://www.leader.ir/az/book/246/1?sn=31668"),
        ("API محتمل 1", "https://www.leader.ir/az/book/246/api/content?sn=31668"),
        ("API محتمل 2", "https://www.leader.ir/az/book/api/246/31668"),
        ("JSON محتمل", "https://www.leader.ir/az/book/246/1?sn=31668&format=json"),
    ]
    
    for name, url in test_urls:
        print(f"\n🔍 تست {name}:")
        print(f"🌐 URL: {url}")
        
        try:
            # تست با headers مختلف
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'application/json, text/html, */*',
                'X-Requested-With': 'XMLHttpRequest',  # برای AJAX
                'Referer': 'https://www.leader.ir/az/book/246/1'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                print(f"📄 Content-Type: {content_type}")
                
                # بررسی اینکه آیا JSON است
                if 'json' in content_type.lower():
                    try:
                        data = response.json()
                        print(f"✅ JSON دریافت شد: {len(str(data))} کاراکتر")
                        print(f"🔑 کلیدهای اصلی: {list(data.keys()) if isinstance(data, dict) else 'نوع دیگر'}")
                    except:
                        print(f"❌ JSON نامعتبر")
                else:
                    # بررسی HTML
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # جستجوی مسائل 48-56
                    found_48_56 = []
                    for num in range(48, 57):
                        if f"Məsələ {num}" in response.text:
                            found_48_56.append(num)
                    
                    print(f"📋 مسائل 48-56 یافت شده: {found_48_56}")
                    
                    # جستجوی script های JavaScript
                    scripts = soup.find_all('script')
                    js_content = ""
                    for script in scripts:
                        if script.string:
                            js_content += script.string
                    
                    # جستجوی API calls در JavaScript
                    api_patterns = [
                        r'fetch\(["\']([^"\']+)["\']',
                        r'ajax\(["\']([^"\']+)["\']',
                        r'\.get\(["\']([^"\']+)["\']',
                        r'url:\s*["\']([^"\']+)["\']',
                    ]
                    
                    found_apis = []
                    for pattern in api_patterns:
                        matches = re.findall(pattern, js_content)
                        found_apis.extend(matches)
                    
                    if found_apis:
                        print(f"🔗 API های یافت شده در JavaScript:")
                        for api in set(found_apis[:5]):  # نمایش 5 مورد اول
                            print(f"  - {api}")
            else:
                print(f"❌ خطا: {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطا: {e}")
    
    # 2. بررسی toggle function
    print(f"\n🔧 روش 2: بررسی toggle function")
    
    # شبیه‌سازی toggle function
    toggle_url = "https://www.leader.ir/az/book/246/1"
    
    try:
        # درخواست POST برای toggle
        toggle_data = {
            'action': 'toggle',
            'id': '31668',
            'sn': '31668'
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-Requested-With': 'XMLHttpRequest',
            'Referer': 'https://www.leader.ir/az/book/246/1'
        }
        
        response = requests.post(toggle_url, data=toggle_data, headers=headers, timeout=30)
        print(f"📊 Toggle POST Status: {response.status_code}")
        
        if response.status_code == 200:
            # بررسی محتوای پاسخ
            if "Məsələ 48" in response.text:
                print(f"✅ مسائل 48-56 در پاسخ toggle یافت شد!")
            else:
                print(f"❌ مسائل 48-56 در پاسخ toggle یافت نشد")
                
    except Exception as e:
        print(f"❌ خطا در toggle: {e}")
    
    # 3. بررسی URL های مستقیم محتوا
    print(f"\n📄 روش 3: بررسی URL های مستقیم محتوا")
    
    direct_urls = [
        f"https://www.leader.ir/az/book/246/content/31668",
        f"https://www.leader.ir/az/book/246/node/31668",
        f"https://www.leader.ir/az/book/246/load/31668",
        f"https://www.leader.ir/az/book/246/get/31668",
    ]
    
    for url in direct_urls:
        try:
            response = requests.get(url, timeout=30)
            print(f"📊 {url}: {response.status_code}")
            
            if response.status_code == 200 and "Məsələ 48" in response.text:
                print(f"✅ محتوای صحیح یافت شد!")
                break
        except:
            print(f"❌ {url}: خطا")


if __name__ == "__main__":
    investigate_dynamic_content()
