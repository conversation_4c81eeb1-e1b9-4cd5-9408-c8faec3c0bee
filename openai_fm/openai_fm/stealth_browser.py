#!/usr/bin/env python3
"""
Stealth Browser Module for OpenAI.fm Automation
ماژول مرورگر مخفی برای اتوماسیون OpenAI.fm
"""

import asyncio
import random
import time
import os
from typing import Optional, Dict, Any, List, Union
from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, ElementHand<PERSON>
from loguru import logger

from .error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AutomationError, ErrorType


class StealthBrowser:
    """مرورگر مخفی با قابلیت‌های ضد تشخیص"""
    
    def __init__(self, 
                 headless: bool = True,
                 viewport_width: Optional[int] = None,
                 viewport_height: Optional[int] = None,
                 user_agent: Optional[str] = None,
                 timeout: int = 30000):
        """
        راه‌اندازی مرورگر مخفی
        
        Args:
            headless: اجرای بدون نمایش
            viewport_width: عرض viewport
            viewport_height: ارتفاع viewport
            user_agent: User agent سفارشی
            timeout: timeout پیش‌فرض (میلی‌ثانیه)
        """
        self.headless = headless
        self.viewport_width = viewport_width or random.randint(1200, 1920)
        self.viewport_height = viewport_height or random.randint(800, 1080)
        self.user_agent = user_agent
        self.timeout = timeout
        
        # اجزای Playwright
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
        # مدیریت خطا
        self.error_handler = ErrorHandler(debug_mode=not headless)
        
        # تنظیمات stealth
        self.stealth_config = {
            "user_agents": [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ],
            "languages": ["en-US,en;q=0.9", "fa-IR,fa;q=0.8,en;q=0.7"],
            "timezones": ["America/New_York", "Europe/London", "Asia/Tehran"]
        }
    
    async def init_browser(self) -> bool:
        """راه‌اندازی مرورگر"""
        try:
            logger.info("🚀 راه‌اندازی مرورگر مخفی...")
            
            self.playwright = await async_playwright().start()
            
            # تنظیمات مرورگر
            browser_args = [
                "--no-sandbox",
                "--disable-setuid-sandbox", 
                "--disable-dev-shm-usage",
                "--disable-accelerated-2d-canvas",
                "--disable-gpu",
                "--window-size=1920,1080",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection"
            ]
            
            if self.headless:
                browser_args.append("--headless=new")
            
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,
                args=browser_args
            )
            
            # ایجاد context با تنظیمات stealth
            context_options = {
                "viewport": {
                    "width": self.viewport_width,
                    "height": self.viewport_height
                },
                "user_agent": self.user_agent or random.choice(self.stealth_config["user_agents"]),
                "locale": "en-US",
                "timezone_id": random.choice(self.stealth_config["timezones"]),
                "permissions": ["geolocation"],
                "extra_http_headers": {
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Accept-Language": random.choice(self.stealth_config["languages"]),
                    "Accept-Encoding": "gzip, deflate, br",
                    "DNT": "1",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1"
                }
            }
            
            self.context = await self.browser.new_context(**context_options)
            
            # ایجاد صفحه
            self.page = await self.context.new_page()
            
            # تنظیم timeout
            self.page.set_default_timeout(self.timeout)
            
            # اعمال stealth scripts
            await self._apply_stealth_scripts()
            
            logger.success("✅ مرورگر مخفی با موفقیت راه‌اندازی شد")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در راه‌اندازی مرورگر")
            return False
    
    async def _apply_stealth_scripts(self):
        """اعمال اسکریپت‌های مخفی‌سازی"""
        stealth_js = """
        // حذف webdriver property
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
        });
        
        // مخفی کردن playwright
        Object.defineProperty(navigator, 'plugins', {
            get: () => [1, 2, 3, 4, 5],
        });
        
        // اضافه کردن chrome property
        window.chrome = {
            runtime: {}
        };
        
        // تغییر user agent
        Object.defineProperty(navigator, 'userAgent', {
            get: () => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        });
        
        // مخفی کردن automation
        const originalQuery = window.document.querySelector;
        window.document.querySelector = function(selector) {
            if (selector === 'webdriver' || selector === 'automation') {
                return null;
            }
            return originalQuery.call(document, selector);
        };
        """
        
        await self.page.add_init_script(stealth_js)
    
    async def navigate_to_site(self, url: str = "https://www.openai.fm/") -> bool:
        """رفتن به سایت مشخص"""
        return await self.navigate_with_stealth(url)
    
    async def navigate_with_stealth(self, url: str) -> bool:
        """
        رفتن به URL با قابلیت‌های مخفی
        
        Args:
            url: آدرس صفحه
            
        Returns:
            bool: موفقیت عملیات
        """
        try:
            logger.info(f"🌐 رفتن به: {url}")
            
            # تاخیر تصادفی
            await asyncio.sleep(random.uniform(1, 3))
            
            # رفتن به صفحه
            response = await self.page.goto(
                url,
                wait_until="domcontentloaded",
                timeout=self.timeout
            )
            
            if response and response.status < 400:
                logger.success(f"✅ با موفقیت به {url} رفت")
                
                # تاخیر برای لود کامل
                await asyncio.sleep(random.uniform(2, 4))
                return True
            else:
                logger.error(f"❌ خطا در رفتن به {url} - Status: {response.status if response else 'Unknown'}")
                return False
                
        except Exception as e:
            self.error_handler.handle_error(e, f"خطا در رفتن به {url}", raise_error=False)
            return False
    
    async def wait_for_element(self, 
                             selector: str, 
                             timeout: Optional[int] = None,
                             state: str = "visible") -> bool:
        """
        انتظار برای ظاهر شدن المان
        
        Args:
            selector: سلکتور المان
            timeout: حداکثر زمان انتظار
            state: حالت مورد انتظار
            
        Returns:
            bool: موجود بودن المان
        """
        try:
            await self.page.wait_for_selector(
                selector, 
                timeout=timeout or self.timeout,
                state=state
            )
            return True
        except Exception as e:
            logger.debug(f"المان {selector} پیدا نشد: {e}")
            return False
    
    async def type_with_human_delay(self, 
                                  selector: str, 
                                  text: str, 
                                  clear_first: bool = True) -> bool:
        """
        تایپ کردن متن با تاخیر انسانی
        
        Args:
            selector: سلکتور المان
            text: متن برای تایپ
            clear_first: پاک کردن قبلی
            
        Returns:
            bool: موفقیت عملیات
        """
        try:
            element = await self.page.wait_for_selector(selector, timeout=self.timeout)
            
            if clear_first:
                await element.clear()
                await asyncio.sleep(random.uniform(0.5, 1))
            
            # تایپ با تاخیر انسانی
            for char in text:
                await element.type(char)
                await asyncio.sleep(random.uniform(0.05, 0.2))
            
            logger.debug(f"✅ متن در {selector} تایپ شد")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, f"خطا در تایپ کردن در {selector}", raise_error=False)
            return False
    
    async def click_with_human_delay(self, selector: str, timeout: Optional[int] = None) -> bool:
        """
        کلیک با تاخیر انسانی
        
        Args:
            selector: سلکتور المان
            timeout: حداکثر زمان انتظار
            
        Returns:
            bool: موفقیت عملیات
        """
        try:
            # انتظار برای المان
            element = await self.page.wait_for_selector(
                selector, 
                timeout=timeout or self.timeout,
                state="visible"
            )
            
            # حرکت موس به المان
            await element.hover()
            await asyncio.sleep(random.uniform(0.5, 1))
            
            # کلیک
            await element.click()
            await asyncio.sleep(random.uniform(1, 2))
            
            logger.debug(f"✅ کلیک روی {selector}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, f"خطا در کلیک روی {selector}", raise_error=False)
            return False
    
    async def evaluate_js(self, script: str) -> Any:
        """اجرای JavaScript"""
        try:
            return await self.page.evaluate(script)
        except Exception as e:
            logger.debug(f"خطا در اجرای JS: {e}")
            return None
    
    async def get_element_text(self, selector: str) -> Optional[str]:
        """دریافت متن المان"""
        try:
            element = await self.page.wait_for_selector(selector, timeout=5000)
            return await element.inner_text()
        except:
            return None
    
    async def take_screenshot(self, path: str = "screenshot.png") -> bool:
        """گرفتن اسکرین‌شات"""
        try:
            await self.page.screenshot(path=path, full_page=True)
            logger.info(f"📸 اسکرین‌شات در {path} ذخیره شد")
            return True
        except Exception as e:
            logger.error(f"خطا در گرفتن اسکرین‌شات: {e}")
            return False
    
    async def wait_for_download(self, timeout: int = 30000) -> Optional[str]:
        """انتظار برای دانلود فایل"""
        try:
            async with self.page.expect_download(timeout=timeout) as download_info:
                download = await download_info.value
                suggested_filename = download.suggested_filename
                
                # ذخیره فایل
                download_path = f"./downloads/{suggested_filename}"
                os.makedirs("./downloads", exist_ok=True)
                await download.save_as(download_path)
                
                logger.success(f"📥 فایل دانلود شد: {download_path}")
                return download_path
                
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در دانلود فایل", raise_error=False)
            return None
    
    async def close(self):
        """بستن مرورگر"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()
                
            logger.info("🔒 مرورگر بسته شد")
            
        except Exception as e:
            logger.error(f"خطا در بستن مرورگر: {e}")
    
    async def __aenter__(self):
        """Context manager entry"""
        await self.init_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        await self.close()


# تابع کمکی برای ایجاد مرورگر مخفی
async def create_stealth_browser(headless: bool = True, **kwargs) -> StealthBrowser:
    """
    ایجاد و راه‌اندازی مرورگر مخفی
    
    Args:
        headless: اجرای بدون نمایش
        **kwargs: تنظیمات اضافی
        
    Returns:
        StealthBrowser: نمونه آماده مرورگر
    """
    browser = StealthBrowser(headless=headless, **kwargs)
    success = await browser.init_browser()
    
    if not success:
        raise AutomationError("خطا در راه‌اندازی مرورگر مخفی", ErrorType.BROWSER_ERROR)
    
    return browser