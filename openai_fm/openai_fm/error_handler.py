#!/usr/bin/env python3
"""
Error Handler Module for OpenAI.fm Automation
ماژول مدیریت خطا برای اتوماسیون OpenAI.fm
"""

import traceback
from enum import Enum
from typing import Optional, Dict, Any, List
from loguru import logger


class ErrorType(Enum):
    """انواع خطاهای اتوماسیون"""
    
    BROWSER_ERROR = "browser_error"
    NETWORK_ERROR = "network_error" 
    ELEMENT_NOT_FOUND = "element_not_found"
    TIMEOUT_ERROR = "timeout_error"
    FILE_ERROR = "file_error"
    VALIDATION_ERROR = "validation_error"
    AUTOMATION_ERROR = "automation_error"
    UNKNOWN_ERROR = "unknown_error"


class AutomationError(Exception):
    """کلاس خطای سفارشی برای اتوماسیون"""
    
    def __init__(self, 
                 message: str,
                 error_type: ErrorType = ErrorType.AUTOMATION_ERROR,
                 details: Optional[Dict[str, Any]] = None,
                 suggestion: Optional[str] = None):
        """
        ایجاد خطای اتوماسیون
        
        Args:
            message: پیام خطا
            error_type: نوع خطا
            details: جزئیات اضافی
            suggestion: پیشنهاد برای حل مشکل
        """
        self.message = message
        self.error_type = error_type
        self.details = details or {}
        self.suggestion = suggestion
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """تبدیل خطا به دیکشنری"""
        return {
            "message": self.message,
            "error_type": self.error_type.value,
            "details": self.details,
            "suggestion": self.suggestion
        }


class ErrorHandler:
    """مدیریت خطاهای اتوماسیون"""
    
    def __init__(self, debug_mode: bool = False):
        """
        راه‌اندازی مدیریت خطا
        
        Args:
            debug_mode: حالت دیباگ
        """
        self.debug_mode = debug_mode
        self.error_count = 0
        self.error_history: List[Dict[str, Any]] = []
        
    def handle_error(self, 
                    error: Exception,
                    context: str = "",
                    raise_error: bool = True) -> Optional[AutomationError]:
        """
        مدیریت خطا
        
        Args:
            error: خطای رخ داده
            context: متن توضیحی
            raise_error: آیا خطا را دوباره پرتاب کند
            
        Returns:
            AutomationError در صورت عدم پرتاب
        """
        self.error_count += 1
        
        # تشخیص نوع خطا
        error_type = self._detect_error_type(error)
        
        # ساخت پیام کامل
        full_message = f"{context}: {str(error)}" if context else str(error)
        
        # ساخت پیشنهاد
        suggestion = self._get_suggestion(error_type, error)
        
        # جزئیات خطا
        details = {
            "original_error": str(error),
            "error_class": error.__class__.__name__,
            "context": context,
            "count": self.error_count
        }
        
        if self.debug_mode:
            details["traceback"] = traceback.format_exc()
        
        # ایجاد AutomationError
        automation_error = AutomationError(
            message=full_message,
            error_type=error_type,
            details=details,
            suggestion=suggestion
        )
        
        # ذخیره در تاریخچه
        self.error_history.append({
            "timestamp": logger.opt(colors=False).info.__defaults__[0],
            "error": automation_error.to_dict()
        })
        
        # لاگ کردن خطا
        logger.error(f"خطا رخ داد: {full_message}")
        if suggestion:
            logger.info(f"💡 پیشنهاد: {suggestion}")
            
        if self.debug_mode:
            logger.debug(f"جزئیات خطا: {details}")
        
        if raise_error:
            raise automation_error
        
        return automation_error
    
    def _detect_error_type(self, error: Exception) -> ErrorType:
        """تشخیص نوع خطا بر اساس Exception"""
        
        error_name = error.__class__.__name__.lower()
        error_message = str(error).lower()
        
        # خطاهای شبکه
        if any(keyword in error_message for keyword in 
               ['connection', 'network', 'timeout', 'dns', 'ssl']):
            return ErrorType.NETWORK_ERROR
            
        # خطاهای timeout
        if 'timeout' in error_name or 'timeout' in error_message:
            return ErrorType.TIMEOUT_ERROR
            
        # خطاهای فایل
        if any(keyword in error_name for keyword in ['file', 'io', 'permission']):
            return ErrorType.FILE_ERROR
            
        # خطاهای المان
        if any(keyword in error_message for keyword in 
               ['element', 'selector', 'not found', 'located']):
            return ErrorType.ELEMENT_NOT_FOUND
            
        # خطاهای مرورگر
        if any(keyword in error_name for keyword in ['playwright', 'browser', 'page']):
            return ErrorType.BROWSER_ERROR
            
        # خطاهای اعتبارسنجی
        if any(keyword in error_name for keyword in ['validation', 'value', 'type']):
            return ErrorType.VALIDATION_ERROR
            
        return ErrorType.UNKNOWN_ERROR
    
    def _get_suggestion(self, error_type: ErrorType, error: Exception) -> str:
        """ارائه پیشنهاد برای حل خطا"""
        
        suggestions = {
            ErrorType.NETWORK_ERROR: "اتصال اینترنت را بررسی کنید و مجدداً تلاش کنید",
            ErrorType.TIMEOUT_ERROR: "timeout را افزایش دهید یا سرعت اینترنت را بررسی کنید",
            ErrorType.ELEMENT_NOT_FOUND: "selector المان را بررسی کنید یا صبر کنید تا صفحه کامل لود شود",
            ErrorType.BROWSER_ERROR: "مرورگر را ری‌استارت کنید یا playwright را دوباره نصب کنید",
            ErrorType.FILE_ERROR: "مجوزهای فایل و دسترسی به مسیر را بررسی کنید",
            ErrorType.VALIDATION_ERROR: "ورودی‌ها و پارامترها را بررسی کنید",
            ErrorType.AUTOMATION_ERROR: "مراحل اتوماسیون را بررسی کنید",
            ErrorType.UNKNOWN_ERROR: "لاگ کامل را بررسی کنید یا حالت debug را فعال کنید"
        }
        
        return suggestions.get(error_type, "خطای ناشناخته - با پشتیبانی تماس بگیرید")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """خلاصه خطاهای رخ داده"""
        
        error_types = {}
        for record in self.error_history:
            error_type = record["error"]["error_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        return {
            "total_errors": self.error_count,
            "error_types": error_types,
            "recent_errors": self.error_history[-5:] if self.error_history else []
        }
    
    def reset_errors(self):
        """پاک کردن تاریخچه خطاها"""
        self.error_count = 0
        self.error_history.clear()
        logger.info("تاریخچه خطاها پاک شد")


# نمونه‌ای از ErrorHandler برای استفاده عمومی
default_error_handler = ErrorHandler()


def handle_automation_error(error: Exception, 
                           context: str = "", 
                           raise_error: bool = True) -> Optional[AutomationError]:
    """تابع کمکی برای مدیریت خطاهای اتوماسیون"""
    return default_error_handler.handle_error(error, context, raise_error)