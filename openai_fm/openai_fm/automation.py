#!/usr/bin/env python3
"""
OpenAI.fm Automation Module
ماژول اتوماسیون OpenAI.fm
"""

import asyncio
import os
import time
import random
import shutil
from typing import Optional, Dict, Any, List
from pathlib import Path
from loguru import logger

from .stealth_browser import StealthBrowser, create_stealth_browser
from .error_handler import Error<PERSON><PERSON><PERSON>, AutomationError, ErrorType


# صداهای موجود در OpenAI.fm
AVAILABLE_VOICES = [
    "Alloy", "Nova", "Echo", "Fable", "Onyx", "Shimmer"
]

# حالت‌های صوتی پیش‌تعریف شده
AVAILABLE_VOICE_AFFECTS = [
    "Natural", "Clear", "Professional", "Friendly", "Warm", "Confident",
    "Calm", "Energetic", "Serious", "Casual", "Emotional", "Neutral"
]


class CustomFilenameAutomation:
    """اتوماسیون تولید صدا با قابلیت نام فایل سفارشی"""
    
    def __init__(self,
                 headless: bool = True,
                 output_directory: str = "./downloads",
                 custom_filename: Optional[str] = None,
                 timeout: int = 60000,
                 max_retries: int = 3):
        """
        راه‌اندازی اتوماسیون
        
        Args:
            headless: اجرای بدون نمایش
            output_directory: مسیر ذخیره فایل‌ها
            custom_filename: نام فایل سفارشی
            timeout: حداکثر زمان انتظار
            max_retries: حداکثر تعداد تلاش مجدد
        """
        self.headless = headless
        self.output_directory = Path(output_directory)
        self.custom_filename = custom_filename
        self.timeout = timeout
        self.max_retries = max_retries
        
        # ایجاد مسیر خروجی
        self.output_directory.mkdir(parents=True, exist_ok=True)
        
        # مدیریت خطا
        self.error_handler = ErrorHandler(debug_mode=not headless)
        
        # متغیرهای کلاس
        self.browser: Optional[StealthBrowser] = None
        self.current_step = ""
        self.steps_completed: List[str] = []
        
        # تنظیمات سایت
        self.site_url = "https://www.openai.fm/"
        self.selectors = {
            "prompt_textarea": "#prompt",
            "input_textarea": "#input", 
            "voice_dropdown": "select",
            "generate_button": "button:has-text('Generate')",
            "play_button": "button:has-text('Play')",
            "download_button": "button:has-text('Download')",
            "audio_player": "audio",
            "loading_indicator": ".loading, .spinner, [class*='loading']"
        }
    
    async def initialize(self) -> bool:
        """راه‌اندازی اولیه"""
        try:
            logger.info("🚀 راه‌اندازی اتوماسیون OpenAI.fm...")
            self.current_step = "initialization"
            
            # ایجاد مرورگر
            self.browser = await create_stealth_browser(
                headless=self.headless,
                timeout=self.timeout
            )
            
            self.steps_completed.append("browser_initialized")
            logger.success("✅ مرورگر راه‌اندازی شد")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در راه‌اندازی اولیه")
            return False
    
    async def navigate_to_site(self) -> bool:
        """رفتن به سایت OpenAI.fm"""
        try:
            self.current_step = "navigation"
            logger.info(f"🌐 رفتن به {self.site_url}")
            
            success = await self.browser.navigate_to_site(self.site_url)
            if success:
                self.steps_completed.append("site_navigation")
                logger.success("✅ به سایت OpenAI.fm رفت")
                return True
            else:
                raise AutomationError("خطا در رفتن به سایت", ErrorType.NETWORK_ERROR)
                
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در رفتن به سایت")
            return False
    
    async def enter_text_content(self, query: str, voice_instructions: Optional[str] = None) -> bool:
        """وارد کردن متن در فیلدهای مربوطه"""
        try:
            self.current_step = "text_input"
            logger.info("✍️ وارد کردن متن...")
            
            # وارد کردن متن اصلی
            success = await self.browser.type_with_human_delay(
                self.selectors["input_textarea"], 
                query
            )
            
            if not success:
                raise AutomationError("خطا در وارد کردن متن اصلی", ErrorType.ELEMENT_NOT_FOUND)
            
            # وارد کردن دستورات صوتی در صورت وجود
            if voice_instructions:
                await asyncio.sleep(random.uniform(1, 2))
                prompt_success = await self.browser.type_with_human_delay(
                    self.selectors["prompt_textarea"],
                    voice_instructions
                )
                
                if not prompt_success:
                    logger.warning("⚠️ خطا در وارد کردن دستورات صوتی، ادامه با متن اصلی")
            
            self.steps_completed.append("text_entered")
            logger.success("✅ متن وارد شد")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در وارد کردن متن")
            return False
    
    async def select_voice(self, voice_name: str) -> bool:
        """انتخاب صدا"""
        try:
            if voice_name not in AVAILABLE_VOICES:
                raise AutomationError(f"صدای نامعتبر: {voice_name}", ErrorType.VALIDATION_ERROR)
            
            self.current_step = "voice_selection"
            logger.info(f"🎤 انتخاب صدا: {voice_name}")
            
            # پیدا کردن dropdown صدا
            await self.browser.wait_for_element(self.selectors["voice_dropdown"])
            
            # انتخاب صدا
            await self.browser.page.select_option(self.selectors["voice_dropdown"], voice_name)
            await asyncio.sleep(random.uniform(1, 2))
            
            self.steps_completed.append("voice_selected")
            logger.success(f"✅ صدا انتخاب شد: {voice_name}")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, f"خطا در انتخاب صدا: {voice_name}")
            return False
    
    async def generate_audio(self) -> bool:
        """تولید فایل صوتی"""
        try:
            self.current_step = "audio_generation"
            logger.info("🎵 شروع تولید صدا...")
            
            # کلیک روی دکمه Generate
            success = await self.browser.click_with_human_delay(self.selectors["generate_button"])
            if not success:
                raise AutomationError("خطا در کلیک روی دکمه Generate", ErrorType.ELEMENT_NOT_FOUND)
            
            # انتظار برای تولید (با نمایش پیشرفت)
            max_wait_time = 60  # 60 ثانیه
            wait_time = 0
            
            while wait_time < max_wait_time:
                await asyncio.sleep(2)
                wait_time += 2
                
                # بررسی اینکه آیا صدا تولید شده
                play_button_visible = await self.browser.wait_for_element(
                    self.selectors["play_button"], 
                    timeout=2000,
                    state="visible"
                )
                
                if play_button_visible:
                    break
                    
                if wait_time % 10 == 0:
                    logger.info(f"⏳ انتظار برای تولید صدا... ({wait_time}s)")
            
            # بررسی نهایی
            if not play_button_visible:
                raise AutomationError("تولید صدا ناموفق - timeout", ErrorType.TIMEOUT_ERROR)
            
            self.steps_completed.append("audio_generated")
            logger.success("✅ صدا تولید شد")
            return True
            
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در تولید صدا")
            return False
    
    async def download_audio(self) -> Optional[str]:
        """دانلود فایل صوتی"""
        try:
            self.current_step = "audio_download"
            logger.info("📥 شروع دانلود...")
            
            # کلیک روی دکمه Download
            download_promise = self.browser.wait_for_download(timeout=30000)
            
            success = await self.browser.click_with_human_delay(self.selectors["download_button"])
            if not success:
                raise AutomationError("خطا در کلیک روی دکمه Download", ErrorType.ELEMENT_NOT_FOUND)
            
            # انتظار برای دانلود
            download_path = await download_promise
            
            if download_path and os.path.exists(download_path):
                self.steps_completed.append("audio_downloaded")
                logger.success(f"✅ فایل دانلود شد: {download_path}")
                return download_path
            else:
                raise AutomationError("فایل دانلود نشد", ErrorType.FILE_ERROR)
                
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در دانلود فایل")
            return None
    
    async def rename_downloaded_file(self, original_path: str) -> str:
        """تغییر نام فایل دانلود شده"""
        try:
            if not self.custom_filename:
                return original_path
            
            self.current_step = "file_rename"
            logger.info(f"📝 تغییر نام فایل به: {self.custom_filename}")
            
            original_file = Path(original_path)
            extension = original_file.suffix or '.mp3'
            
            # مسیر جدید
            new_filename = f"{self.custom_filename}{extension}"
            new_path = self.output_directory / new_filename
            
            # انتقال و تغییر نام
            shutil.move(str(original_file), str(new_path))
            
            self.steps_completed.append("file_renamed")
            logger.success(f"✅ نام فایل تغییر کرد: {new_path}")
            return str(new_path)
            
        except Exception as e:
            self.error_handler.handle_error(e, "خطا در تغییر نام فایل", raise_error=False)
            return original_path
    
    async def run_complete_workflow(self, 
                                  query: str,
                                  voice_instructions: Optional[str] = None,
                                  voice_name: str = "Alloy") -> Dict[str, Any]:
        """اجرای فرآیند کامل (بدون custom filename)"""
        return await self.run_complete_workflow_with_custom_name(
            query=query,
            voice_instructions=voice_instructions,
            voice_name=voice_name
        )
    
    async def run_complete_workflow_with_custom_name(self,
                                                   query: str,
                                                   voice_instructions: Optional[str] = None,
                                                   voice_name: str = "Alloy") -> Dict[str, Any]:
        """
        اجرای فرآیند کامل تولید صدا با نام سفارشی
        
        Args:
            query: متن برای تبدیل به گفتار
            voice_instructions: دستورات حالت صدا
            voice_name: نام صدا
            
        Returns:
            Dict شامل نتیجه عملیات
        """
        start_time = time.time()
        result = {
            "success": False,
            "download_path": None,
            "error": None,
            "steps_completed": [],
            "execution_time": 0,
            "custom_filename": self.custom_filename,
            "voice_name": voice_name
        }
        
        try:
            logger.info("🎬 شروع فرآیند کامل تولید صدا...")
            
            # مرحله 1: راه‌اندازی
            if not await self.initialize():
                raise AutomationError("خطا در راه‌اندازی", ErrorType.AUTOMATION_ERROR)
            
            # مرحله 2: رفتن به سایت
            if not await self.navigate_to_site():
                raise AutomationError("خطا در رفتن به سایت", ErrorType.NETWORK_ERROR)
            
            # مرحله 3: وارد کردن متن
            if not await self.enter_text_content(query, voice_instructions):
                raise AutomationError("خطا در وارد کردن متن", ErrorType.AUTOMATION_ERROR)
            
            # مرحله 4: انتخاب صدا
            if not await self.select_voice(voice_name):
                raise AutomationError("خطا در انتخاب صدا", ErrorType.AUTOMATION_ERROR)
            
            # مرحله 5: تولید صدا
            if not await self.generate_audio():
                raise AutomationError("خطا در تولید صدا", ErrorType.AUTOMATION_ERROR)
            
            # مرحله 6: دانلود فایل
            original_path = await self.download_audio()
            if not original_path:
                raise AutomationError("خطا در دانلود فایل", ErrorType.FILE_ERROR)
            
            # مرحله 7: تغییر نام فایل (در صورت لزوم)
            final_path = await self.rename_downloaded_file(original_path)
            
            # تکمیل موفق
            result["success"] = True
            result["download_path"] = final_path
            result["steps_completed"] = self.steps_completed.copy()
            
            execution_time = time.time() - start_time
            result["execution_time"] = round(execution_time, 2)
            
            logger.success(f"🎉 فرآیند کامل در {execution_time:.1f} ثانیه تکمیل شد!")
            logger.success(f"📁 فایل: {final_path}")
            
        except Exception as e:
            result["error"] = str(e)
            result["steps_completed"] = self.steps_completed.copy()
            result["execution_time"] = round(time.time() - start_time, 2)
            
            logger.error(f"❌ خطا در فرآیند: {e}")
            
        finally:
            await self.cleanup()
        
        return result
    
    async def cleanup(self):
        """تمیز کردن منابع"""
        try:
            if self.browser:
                await self.browser.close()
                self.browser = None
            
            logger.info("🧹 منابع تمیز شدند")
            
        except Exception as e:
            logger.error(f"خطا در تمیز کردن منابع: {e}")
    
    def get_progress_info(self) -> Dict[str, Any]:
        """دریافت اطلاعات پیشرفت"""
        return {
            "current_step": self.current_step,
            "steps_completed": self.steps_completed.copy(),
            "total_steps": 7,
            "progress_percentage": len(self.steps_completed) / 7 * 100
        }


# تابع کمکی برای اجرای سریع
async def quick_generate_audio(query: str,
                             voice_name: str = "Alloy",
                             voice_instructions: Optional[str] = None,
                             output_directory: str = "./downloads",
                             custom_filename: Optional[str] = None,
                             headless: bool = True) -> Dict[str, Any]:
    """
    تولید سریع صدا با پارامترهای ساده
    
    Args:
        query: متن برای تبدیل
        voice_name: نام صدا
        voice_instructions: دستورات صوتی
        output_directory: مسیر ذخیره
        custom_filename: نام فایل سفارشی
        headless: حالت بدون نمایش
        
    Returns:
        Dict نتیجه عملیات
    """
    automation = CustomFilenameAutomation(
        headless=headless,
        output_directory=output_directory,
        custom_filename=custom_filename
    )
    
    return await automation.run_complete_workflow_with_custom_name(
        query=query,
        voice_instructions=voice_instructions,
        voice_name=voice_name
    )