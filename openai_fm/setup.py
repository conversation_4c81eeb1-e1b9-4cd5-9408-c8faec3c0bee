#!/usr/bin/env python3
"""
Setup script for OpenAI.fm Automation Package
اسکریپت نصب برای پکیج اتوماسیون OpenAI.fm
"""

from setuptools import setup, find_packages

# خواندن فایل requirements
with open("requirements.txt", "r", encoding="utf-8") as f:
    requirements = [line.strip() for line in f if line.strip() and not line.startswith("#")]

# خواندن README
with open("docs/README.md", "r", encoding="utf-8") as f:
    long_description = f.read()

setup(
    name="openai-fm-automation",
    version="2.0.0",
    author="OpenAI.fm Automation Team",
    author_email="<EMAIL>",
    description="Advanced OpenAI.fm automation with anti-detection features",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/openai-fm-automation",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Multimedia :: Sound/Audio :: Speech",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.0.0",
        ],
        "docs": [
            "sphinx>=7.0.0",
            "sphinx-rtd-theme>=1.3.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "openai-fm-server=openai_fm.main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "openai_fm": ["docs/*", "tests/*"],
    },
    keywords=[
        "openai",
        "text-to-speech",
        "automation",
        "web-scraping",
        "playwright",
        "anti-detection",
        "tts",
        "speech-synthesis"
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-username/openai-fm-automation/issues",
        "Source": "https://github.com/your-username/openai-fm-automation",
        "Documentation": "https://github.com/your-username/openai-fm-automation/docs",
    },
)
