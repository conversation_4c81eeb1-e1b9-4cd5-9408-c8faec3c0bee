# OpenAI.fm Automation System

🎯 **Advanced text-to-speech automation with custom filename support and anti-detection features**

## 🚀 Quick Start

```bash
# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium

# Run the API server
python main.py
```

The API will be available at `http://localhost:8000`

## 📁 Project Structure

```
openai_fm/
├── main.py                    # 🌐 Main API server
├── requirements.txt           # 📦 Dependencies
├── setup.py                  # 🔧 Package setup
├── README.md                 # 📖 This file
├── openai_fm/                # 🎯 Core package
│   ├── __init__.py
│   ├── automation.py         # 🤖 Automation engine
│   ├── stealth_browser.py    # 🛡️ Anti-detection browser
│   └── error_handler.py      # ⚠️ Error management
├── tests/                    # 🧪 Test modules
│   ├── __init__.py
│   ├── test_automation.py    # 🔬 Automation tests
│   └── test_api.py          # 🌐 API tests
└── docs/                     # 📚 Documentation
    ├── README.md            # 📖 Detailed documentation
    ├── API.md               # 🌐 API reference
    └── tutorial_persian.html # 🇮🇷 Persian tutorial
```

## ✨ Key Features

- ✅ **Custom Filenames**: Download files with your preferred names
- ✅ **Custom Directories**: Organize files in specific folders  
- ✅ **Anti-Detection**: Advanced stealth techniques
- ✅ **REST API**: FastAPI-based endpoints
- ✅ **Error Handling**: Comprehensive error management
- ✅ **Voice Selection**: All OpenAI.fm voices supported
- ✅ **Async Processing**: Background task processing

## 🎭 Available Voices

**Voices**: Alloy, Ash, Ballad, Coral, Echo, Fable, Onyx, Nova, Sage, Shimmer, Verse

**Voice Affects**: Calm, Sincere, Serene, Patient Teacher, Friendly

## 🔧 Usage Examples

### Direct Usage
```python
import asyncio
from openai_fm.automation import CustomFilenameAutomation

async def main():
    automation = CustomFilenameAutomation(
        headless=True,
        output_directory="./my_audio",
        custom_filename="my_speech"
    )
    
    result = await automation.run_complete_workflow_with_custom_name(
        query="Hello, this is a test.",
        voice_name="Alloy"
    )
    
    print(f"Success: {result['success']}")
    print(f"File: {result['download_path']}")

asyncio.run(main())
```

### API Usage
```bash
curl -X POST "http://localhost:8000/api/generate" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Hello world",
    "voice_name": "Nova",
    "custom_filename": "hello_world",
    "output_directory": "./downloads"
  }'
```

## 🧪 Testing

```bash
# Run automation tests
python tests/test_automation.py

# Run API tests (requires running server)
python tests/test_api.py
```

## 📚 Documentation

- **[Complete Documentation](docs/README.md)** - Detailed setup and usage guide
- **[API Reference](docs/API.md)** - Complete API documentation  
- **[Persian Tutorial](docs/tutorial_persian.html)** - Comprehensive Persian guide

## 🛡️ Anti-Detection Features

- User Agent Rotation
- Viewport Randomization  
- Human-like Behavior Simulation
- Stealth JavaScript Injection
- Realistic HTTP Headers

## ⚙️ Configuration

Create a `.env` file:
```env
API_HOST=0.0.0.0
API_PORT=8000
HEADLESS_MODE=true
GENERATION_TIMEOUT=120
DOWNLOAD_TIMEOUT=60
```

## 🔍 Troubleshooting

1. **Browser fails to start**: Run `playwright install chromium`
2. **Elements not found**: Website structure may have changed
3. **Generation timeout**: Try shorter text or increase timeout

## 📄 License

Educational and research purposes. Please respect OpenAI.fm's terms of service.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

---

**⚠️ Disclaimer**: This tool is for educational purposes. Users are responsible for compliance with applicable terms of service and laws.
