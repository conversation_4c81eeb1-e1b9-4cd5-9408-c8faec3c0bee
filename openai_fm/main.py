#!/usr/bin/env python3
"""
OpenAI.fm Automation API Server
سرور API اصلی برای اتوماسیون OpenAI.fm
"""

import asyncio
import os
import uuid
from datetime import datetime
from typing import Optional, List
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from loguru import logger
import uvicorn

from openai_fm.automation import CustomFilenameAutomation, AVAILABLE_VOICES, AVAILABLE_VOICE_AFFECTS

# تنظیم لاگ‌گیری
logger.add("logs/api_server.log", rotation="1 day", retention="7 days")

app = FastAPI(
    title="OpenAI.fm Automation API",
    description="سیستم پیشرفته تبدیل متن به گفتار با قابلیت‌های سفارشی‌سازی",
    version="2.0.0"
)

# اضافه کردن CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# مدل‌های درخواست/پاسخ
class TTSRequest(BaseModel):
    query: str = Field(..., description="متن برای تبدیل به گفتار", min_length=1, max_length=5000)
    voice_instructions: Optional[str] = Field(None, description="دستورات حالت و تن صدا", max_length=2000)
    voice_name: str = Field("Alloy", description="نام صدا برای تولید")
    voice_affect: Optional[str] = Field(None, description="حالت صوتی از پیش تعریف شده")
    custom_filename: Optional[str] = Field(None, description="نام فایل سفارشی (بدون پسوند)")
    output_directory: Optional[str] = Field("./downloads", description="مسیر ذخیره فایل")
    language_hint: Optional[str] = Field("fa", description="راهنمای زبان (fa, en, ar, etc.)")

class TTSResponse(BaseModel):
    success: bool
    task_id: str
    message: str
    download_url: Optional[str] = None
    error: Optional[str] = None
    steps_completed: List[str] = []
    output_directory: Optional[str] = None
    custom_filename: Optional[str] = None

class StatusResponse(BaseModel):
    task_id: str
    status: str  # "pending", "processing", "completed", "failed"
    progress: str
    download_url: Optional[str] = None
    error: Optional[str] = None
    output_directory: Optional[str] = None
    final_filename: Optional[str] = None

# ذخیره‌سازی تسک‌ها در حافظه
tasks = {}

@app.get("/")
async def root():
    """صفحه اصلی با اطلاعات API"""
    return {
        "message": "OpenAI.fm Automation API",
        "version": "2.0.0",
        "description": "سیستم پیشرفته تبدیل متن به گفتار",
        "endpoints": {
            "generate": "/api/generate",
            "status": "/api/status/{task_id}",
            "download": "/api/download/{filename}",
            "voices": "/api/voices",
            "health": "/api/health"
        }
    }

@app.get("/api/health")
async def health_check():
    """بررسی سلامت سرور"""
    return {
        "status": "healthy", 
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0",
        "active_tasks": len([t for t in tasks.values() if t["status"] == "processing"])
    }

@app.get("/api/voices")
async def get_available_voices():
    """دریافت لیست صداها و حالت‌های صوتی موجود"""
    return {
        "voices": AVAILABLE_VOICES,
        "voice_affects": AVAILABLE_VOICE_AFFECTS,
        "supported_languages": ["fa", "en", "ar", "tr", "ur"],
        "total_voices": len(AVAILABLE_VOICES),
        "total_affects": len(AVAILABLE_VOICE_AFFECTS)
    }

@app.post("/api/generate", response_model=TTSResponse)
async def generate_speech(request: TTSRequest, background_tasks: BackgroundTasks):
    """تولید فایل صوتی با قابلیت‌های پیشرفته"""
    
    # اعتبارسنجی نام صدا
    if request.voice_name not in AVAILABLE_VOICES:
        raise HTTPException(
            status_code=400, 
            detail=f"نام صدای نامعتبر. صداهای موجود: {AVAILABLE_VOICES}"
        )
    
    # اعتبارسنجی حالت صوتی در صورت وجود
    if request.voice_affect and request.voice_affect not in AVAILABLE_VOICE_AFFECTS:
        raise HTTPException(
            status_code=400,
            detail=f"حالت صوتی نامعتبر. حالت‌های موجود: {AVAILABLE_VOICE_AFFECTS}"
        )
    
    # اعتبارسنجی مسیر خروجی
    try:
        os.makedirs(request.output_directory, exist_ok=True)
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"خطا در ایجاد مسیر خروجی: {str(e)}"
        )
    
    # تولید شناسه تسک منحصر به فرد
    task_id = str(uuid.uuid4())
    
    # راه‌اندازی وضعیت تسک
    tasks[task_id] = {
        "status": "pending",
        "progress": "تسک ایجاد شد",
        "created_at": datetime.now(),
        "download_url": None,
        "error": None,
        "steps_completed": [],
        "output_directory": request.output_directory,
        "custom_filename": request.custom_filename,
        "final_filename": None
    }
    
    # ترکیب دستورات صوتی با حالت صوتی در صورت وجود
    voice_instructions = request.voice_instructions or ""
    if request.voice_affect:
        affect_instruction = f"Voice Affect: {request.voice_affect}. "
        voice_instructions = affect_instruction + voice_instructions
    
    # اضافه کردن راهنمای زبان
    if request.language_hint:
        language_instruction = f"Language: {request.language_hint}. "
        voice_instructions = language_instruction + voice_instructions
    
    # شروع تسک در پس‌زمینه
    background_tasks.add_task(
        process_tts_request,
        task_id,
        request.query,
        voice_instructions,
        request.voice_name,
        request.custom_filename,
        request.output_directory
    )
    
    logger.info(f"تسک TTS {task_id} برای متن ایجاد شد: {request.query[:50]}...")
    
    return TTSResponse(
        success=True,
        task_id=task_id,
        message="تولید فایل صوتی شروع شد",
        steps_completed=["task_created"],
        output_directory=request.output_directory,
        custom_filename=request.custom_filename
    )

@app.get("/api/status/{task_id}", response_model=StatusResponse)
async def get_task_status(task_id: str):
    """دریافت وضعیت یک تسک تولید صدا"""
    
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="تسک پیدا نشد")
    
    task = tasks[task_id]
    
    return StatusResponse(
        task_id=task_id,
        status=task["status"],
        progress=task["progress"],
        download_url=task["download_url"],
        error=task["error"],
        output_directory=task["output_directory"],
        final_filename=task["final_filename"]
    )

@app.get("/api/download/{filename}")
async def download_file(filename: str, directory: str = "./downloads"):
    """دانلود فایل صوتی تولید شده"""
    
    file_path = os.path.join(directory, filename)
    
    if not os.path.exists(file_path):
        # جستجو در مسیرهای مختلف
        possible_paths = ["./downloads", "./", "./output"]
        for path in possible_paths:
            test_path = os.path.join(path, filename)
            if os.path.exists(test_path):
                file_path = test_path
                break
        else:
            raise HTTPException(status_code=404, detail="فایل پیدا نشد")
    
    return FileResponse(
        path=file_path,
        filename=filename,
        media_type='audio/mpeg'
    )

async def process_tts_request(task_id: str, query: str, voice_instructions: str, 
                             voice_name: str, custom_filename: str = None, 
                             output_directory: str = "./downloads"):
    """پردازش درخواست TTS در پس‌زمینه"""
    
    try:
        # به‌روزرسانی وضعیت تسک
        tasks[task_id]["status"] = "processing"
        tasks[task_id]["progress"] = "در حال راه‌اندازی اتوماسیون"
        
        logger.info(f"شروع پردازش تسک TTS {task_id}")
        
        # ایجاد نمونه اتوماسیون پیشرفته
        automation = CustomFilenameAutomation(
            headless=True,
            output_directory=output_directory,
            custom_filename=custom_filename
        )
        
        # اجرای فرآیند کامل
        result = await automation.run_complete_workflow_with_custom_name(
            query=query,
            voice_instructions=voice_instructions if voice_instructions.strip() else None,
            voice_name=voice_name
        )
        
        # به‌روزرسانی تسک با نتیجه
        if result["success"]:
            filename = os.path.basename(result["download_path"])
            tasks[task_id]["status"] = "completed"
            tasks[task_id]["progress"] = "تولید صدا با موفقیت تکمیل شد"
            tasks[task_id]["download_url"] = f"/api/download/{filename}?directory={output_directory}"
            tasks[task_id]["steps_completed"] = result["steps_completed"]
            tasks[task_id]["final_filename"] = filename
            
            logger.info(f"تسک TTS {task_id} با موفقیت تکمیل شد")
            
        else:
            tasks[task_id]["status"] = "failed"
            tasks[task_id]["progress"] = "تولید صدا ناموفق"
            tasks[task_id]["error"] = result["error"]
            tasks[task_id]["steps_completed"] = result["steps_completed"]
            
            logger.error(f"تسک TTS {task_id} ناموفق: {result['error']}")
            
    except Exception as e:
        tasks[task_id]["status"] = "failed"
        tasks[task_id]["progress"] = "خطای غیرمنتظره رخ داد"
        tasks[task_id]["error"] = str(e)
        
        logger.error(f"خطا در تسک TTS {task_id}: {e}")

@app.delete("/api/tasks/{task_id}")
async def delete_task(task_id: str):
    """حذف یک تسک و فایل‌های مرتبط"""
    
    if task_id not in tasks:
        raise HTTPException(status_code=404, detail="تسک پیدا نشد")
    
    task = tasks[task_id]
    
    # حذف فایل مرتبط در صورت وجود
    if task["download_url"] and task["final_filename"]:
        file_path = os.path.join(task["output_directory"], task["final_filename"])
        if os.path.exists(file_path):
            os.remove(file_path)
    
    # حذف تسک از حافظه
    del tasks[task_id]
    
    return {"message": "تسک با موفقیت حذف شد"}

@app.get("/api/tasks")
async def list_tasks():
    """لیست تمام تسک‌ها (برای دیباگ/نظارت)"""
    return {
        "tasks": [
            {
                "task_id": task_id,
                "status": task_data["status"],
                "created_at": task_data["created_at"].isoformat(),
                "progress": task_data["progress"],
                "output_directory": task_data["output_directory"],
                "custom_filename": task_data["custom_filename"]
            }
            for task_id, task_data in tasks.items()
        ],
        "total_tasks": len(tasks),
        "active_tasks": len([t for t in tasks.values() if t["status"] == "processing"]),
        "completed_tasks": len([t for t in tasks.values() if t["status"] == "completed"]),
        "failed_tasks": len([t for t in tasks.values() if t["status"] == "failed"])
    }

if __name__ == "__main__":
    # ایجاد پوشه لاگ‌ها
    os.makedirs("logs", exist_ok=True)
    os.makedirs("downloads", exist_ok=True)
    
    # اجرای سرور
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
