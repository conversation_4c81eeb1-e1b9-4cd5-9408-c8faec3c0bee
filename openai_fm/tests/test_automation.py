#!/usr/bin/env python3
"""
Comprehensive Test Suite for OpenAI.fm Automation
مجموعه تست جامع برای اتوماسیون OpenAI.fm
"""

import asyncio
import os
import pytest
from loguru import logger

from openai_fm.automation import CustomFilenameAutomation, AVAILABLE_VOICES
from openai_fm.stealth_browser import StealthBrowser

class TestOpenAIFMAutomation:
    """مجموعه تست برای اتوماسیون OpenAI.fm"""
    
    @pytest.fixture
    async def automation(self):
        """ایجاد نمونه اتوماسیون برای تست"""
        automation = CustomFilenameAutomation(
            headless=True,
            output_directory="./test_output"
        )
        yield automation
        await automation.cleanup()
        
    async def test_browser_initialization(self):
        """تست راه‌اندازی مرورگر"""
        browser = StealthBrowser(headless=True)
        
        try:
            await browser.init_browser()
            assert browser.browser is not None
            assert browser.context is not None
            assert browser.page is not None
            logger.info("✅ Browser initialization test passed")
            
        finally:
            await browser.close()
            
    async def test_website_navigation(self):
        """تست رفتن به OpenAI.fm"""
        browser = StealthBrowser(headless=True)
        
        try:
            await browser.init_browser()
            success = await browser.navigate_with_stealth("https://www.openai.fm/")
            assert success, "Failed to navigate to OpenAI.fm"
            
            # بررسی اینکه در صفحه درست هستیم
            title = await browser.evaluate_js("document.title")
            assert "OpenAI.fm" in title
            logger.info("✅ Website navigation test passed")
            
        finally:
            await browser.close()
            
    async def test_element_detection(self):
        """تست تشخیص عناصر کلیدی در صفحه"""
        browser = StealthBrowser(headless=True)
        
        try:
            await browser.init_browser()
            await browser.navigate_with_stealth("https://www.openai.fm/")
            
            # بررسی عناصر ورودی
            prompt_exists = await browser.evaluate_js("!!document.querySelector('#prompt')")
            input_exists = await browser.evaluate_js("!!document.querySelector('#input')")
            
            assert prompt_exists, "Prompt textarea not found"
            assert input_exists, "Input textarea not found"
            
            # بررسی دکمه‌ها
            play_button_exists = await browser.evaluate_js(
                "!!document.querySelector('div:has-text(\"Play\")')"
            )
            download_button_exists = await browser.evaluate_js(
                "!!document.querySelector('div:has-text(\"Download\")')"
            )
            
            assert play_button_exists, "Play button not found"
            assert download_button_exists, "Download button not found"
            
            logger.info("✅ Element detection test passed")
            
        finally:
            await browser.close()
            
    async def test_text_input(self):
        """تست قابلیت ورود متن"""
        browser = StealthBrowser(headless=True)
        
        try:
            await browser.init_browser()
            await browser.navigate_with_stealth("https://www.openai.fm/")
            
            test_text = "This is a test message for automation."
            
            # تست ورود متن در prompt
            success = await browser.type_with_human_delay("#prompt", test_text)
            assert success, "Failed to input text into prompt field"
            
            # تأیید اینکه متن وارد شده
            entered_text = await browser.evaluate_js("document.querySelector('#prompt').value")
            assert test_text in entered_text, "Text was not properly entered"
            
            logger.info("✅ Text input test passed")
            
        finally:
            await browser.close()
            
    async def test_voice_selection(self):
        """تست قابلیت انتخاب صدا"""
        automation = CustomFilenameAutomation(
            headless=True,
            output_directory="./test_output"
        )
        
        try:
            await automation.initialize()
            await automation.navigate_to_site()
            
            # تست انتخاب صداهای مختلف
            for voice in AVAILABLE_VOICES[:3]:  # تست 3 صدای اول
                success = await automation.select_voice(voice)
                assert success, f"Failed to select voice: {voice}"
                
            logger.info("✅ Voice selection test passed")
            
        finally:
            await automation.cleanup()
            
    async def test_custom_filename_functionality(self):
        """تست قابلیت نام فایل سفارشی"""
        automation = CustomFilenameAutomation(
            headless=True,
            output_directory="./test_output",
            custom_filename="test_custom_file"
        )
        
        try:
            # بررسی تنظیمات
            assert automation.custom_filename == "test_custom_file"
            assert automation.output_directory == "./test_output"
            assert os.path.exists("./test_output")
            
            logger.info("✅ Custom filename functionality test passed")
            
        finally:
            await automation.cleanup()
            
    async def test_error_handling(self):
        """تست مکانیزم‌های مدیریت خطا"""
        
        # تست با selector نامعتبر
        browser = StealthBrowser(headless=True)
        
        try:
            await browser.init_browser()
            await browser.navigate_with_stealth("https://www.openai.fm/")
            
            # این باید به صورت مناسب شکست بخورد
            success = await browser.click_with_human_delay("#nonexistent-element")
            assert not success, "Should have failed with non-existent element"
            
            logger.info("✅ Error handling test passed")
            
        finally:
            await browser.close()

async def run_integration_test():
    """تست یکپارچگی کامل (اختیاری - نیاز به زمان بیشتر)"""
    logger.info("🧪 Starting integration test...")
    
    automation = CustomFilenameAutomation(
        headless=True,
        output_directory="./integration_test",
        custom_filename="integration_test_audio"
    )
    
    try:
        result = await automation.run_complete_workflow_with_custom_name(
            query="This is an integration test of the automation system.",
            voice_instructions="Voice Affect: Clear and professional.",
            voice_name="Alloy"
        )
        
        # بررسی ساختار نتیجه
        assert "success" in result
        assert "download_path" in result
        assert "error" in result
        assert "steps_completed" in result
        
        if result["success"]:
            assert result["download_path"] is not None
            assert os.path.exists(result["download_path"])
            logger.info(f"✅ Integration test passed - File: {result['download_path']}")
        else:
            logger.warning(f"⚠️ Integration test failed but structure is correct: {result['error']}")
            
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        raise
        
    finally:
        await automation.cleanup()

async def run_all_tests():
    """اجرای تمام تست‌ها"""
    logger.info("🚀 Starting comprehensive test suite...")
    
    test_instance = TestOpenAIFMAutomation()
    
    tests = [
        test_instance.test_browser_initialization,
        test_instance.test_website_navigation,
        test_instance.test_element_detection,
        test_instance.test_text_input,
        test_instance.test_voice_selection,
        test_instance.test_custom_filename_functionality,
        test_instance.test_error_handling
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            await test()
            passed += 1
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed: {e}")
            failed += 1
    
    logger.info(f"\n📊 Test Results: {passed} passed, {failed} failed")
    
    # اجرای تست یکپارچگی (اختیاری)
    try:
        await run_integration_test()
        logger.info("✅ Integration test completed")
    except Exception as e:
        logger.warning(f"⚠️ Integration test skipped: {e}")
    
    return passed, failed

if __name__ == "__main__":
    # اجرای تست‌ها
    asyncio.run(run_all_tests())
