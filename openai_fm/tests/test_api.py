#!/usr/bin/env python3
"""
API Test Suite for OpenAI.fm Automation
مجموعه تست API برای اتوماسیون OpenAI.fm
"""

import asyncio
import pytest
import httpx
from loguru import logger

class TestOpenAIFMAPI:
    """مجموعه تست برای API"""
    
    def __init__(self):
        self.base_url = "http://localhost:8000"
        
    async def test_health_endpoint(self):
        """تست endpoint سلامت"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/api/health")
                assert response.status_code == 200
                data = response.json()
                assert "status" in data
                assert data["status"] == "healthy"
                logger.info("✅ Health endpoint test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Health endpoint test failed: {e}")
                return False
                
    async def test_voices_endpoint(self):
        """تست endpoint صداها"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/api/voices")
                assert response.status_code == 200
                data = response.json()
                assert "voices" in data
                assert "voice_affects" in data
                assert len(data["voices"]) > 0
                logger.info("✅ Voices endpoint test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Voices endpoint test failed: {e}")
                return False
                
    async def test_generate_endpoint(self):
        """تست endpoint تولید"""
        async with httpx.AsyncClient() as client:
            try:
                test_request = {
                    "query": "This is a test of the API endpoint.",
                    "voice_instructions": "Voice Affect: Clear and professional.",
                    "voice_name": "Alloy",
                    "custom_filename": "api_test_audio"
                }
                
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=test_request
                )
                assert response.status_code == 200
                data = response.json()
                assert "task_id" in data
                assert "success" in data
                assert data["success"] is True
                
                task_id = data["task_id"]
                logger.info(f"✅ Generate endpoint test passed - Task ID: {task_id}")
                return task_id
            except Exception as e:
                logger.error(f"❌ Generate endpoint test failed: {e}")
                return None
                
    async def test_status_endpoint(self, task_id: str):
        """تست endpoint وضعیت"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/api/status/{task_id}")
                assert response.status_code == 200
                data = response.json()
                assert "task_id" in data
                assert "status" in data
                assert data["task_id"] == task_id
                logger.info("✅ Status endpoint test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Status endpoint test failed: {e}")
                return False
                
    async def test_tasks_endpoint(self):
        """تست endpoint لیست تسک‌ها"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(f"{self.base_url}/api/tasks")
                assert response.status_code == 200
                data = response.json()
                assert "tasks" in data
                assert "total_tasks" in data
                logger.info("✅ Tasks endpoint test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Tasks endpoint test failed: {e}")
                return False
                
    async def test_invalid_voice_name(self):
        """تست نام صدای نامعتبر"""
        async with httpx.AsyncClient() as client:
            try:
                test_request = {
                    "query": "Test with invalid voice",
                    "voice_name": "InvalidVoice"
                }
                
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=test_request
                )
                assert response.status_code == 400  # باید خطا برگرداند
                logger.info("✅ Invalid voice name test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Invalid voice name test failed: {e}")
                return False
                
    async def test_missing_query(self):
        """تست درخواست بدون متن"""
        async with httpx.AsyncClient() as client:
            try:
                test_request = {
                    "voice_name": "Alloy"
                    # query مفقود
                }
                
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=test_request
                )
                assert response.status_code == 422  # Validation error
                logger.info("✅ Missing query test passed")
                return True
            except Exception as e:
                logger.error(f"❌ Missing query test failed: {e}")
                return False

async def run_api_tests():
    """اجرای تمام تست‌های API"""
    logger.info("🚀 Starting API test suite...")
    
    api_test = TestOpenAIFMAPI()
    
    # بررسی در دسترس بودن API
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{api_test.base_url}/api/health", timeout=5.0)
            if response.status_code != 200:
                logger.error("❌ API server is not running. Please start the server first:")
                logger.error("python main.py")
                return 0, 1
    except Exception:
        logger.error("❌ API server is not accessible. Please start the server first:")
        logger.error("python main.py")
        return 0, 1
    
    tests = [
        api_test.test_health_endpoint,
        api_test.test_voices_endpoint,
        api_test.test_tasks_endpoint,
        api_test.test_invalid_voice_name,
        api_test.test_missing_query
    ]
    
    passed = 0
    failed = 0
    
    # اجرای تست‌های پایه
    for test in tests:
        try:
            result = await test()
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            logger.error(f"❌ Test {test.__name__} failed: {e}")
            failed += 1
    
    # تست تولید و وضعیت (اگر سرور در دسترس باشد)
    try:
        task_id = await api_test.test_generate_endpoint()
        if task_id:
            passed += 1
            # تست وضعیت
            if await api_test.test_status_endpoint(task_id):
                passed += 1
            else:
                failed += 1
        else:
            failed += 1
    except Exception as e:
        logger.error(f"❌ Generate/Status tests failed: {e}")
        failed += 2
    
    logger.info(f"\n📊 API Test Results: {passed} passed, {failed} failed")
    return passed, failed

if __name__ == "__main__":
    # اجرای تست‌های API
    asyncio.run(run_api_tests())
