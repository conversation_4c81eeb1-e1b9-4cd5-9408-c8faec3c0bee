#!/usr/bin/env python3
"""
Example Usage of OpenAI.fm Automation
نمونه استفاده از اتوماسیون OpenAI.fm
"""

import asyncio
from openai_fm.automation import CustomFilenameAutomation

async def basic_example():
    """مثال پایه استفاده از سیستم"""
    print("🎯 Basic Example: Simple text-to-speech generation")
    print("=" * 50)
    
    automation = CustomFilenameAutomation(
        headless=True,
        output_directory="./examples_output",
        custom_filename="basic_example"
    )
    
    result = await automation.run_complete_workflow_with_custom_name(
        query="Hello, this is a basic example of OpenAI.fm automation.",
        voice_instructions="Voice Affect: Clear and professional.",
        voice_name="Alloy"
    )
    
    if result["success"]:
        print(f"✅ Success! Audio saved to: {result['download_path']}")
    else:
        print(f"❌ Failed: {result['error']}")
    
    return result

async def custom_filename_example():
    """مثال با نام فایل سفارشی"""
    print("\n🎯 Custom Filename Example")
    print("=" * 50)
    
    automation = CustomFilenameAutomation(
        headless=True,
        output_directory="./custom_audio",
        custom_filename="my_custom_speech_file"
    )
    
    result = await automation.run_complete_workflow_with_custom_name(
        query="This audio file will be saved with a custom filename.",
        voice_instructions="Voice Affect: Friendly and warm.",
        voice_name="Nova"
    )
    
    if result["success"]:
        print(f"✅ Custom file created: {result['download_path']}")
    else:
        print(f"❌ Failed: {result['error']}")
    
    return result

async def multiple_voices_example():
    """مثال با صداهای مختلف"""
    print("\n🎯 Multiple Voices Example")
    print("=" * 50)
    
    voices_to_test = ["Alloy", "Nova", "Echo"]
    results = []
    
    for i, voice in enumerate(voices_to_test):
        print(f"Testing voice {i+1}/{len(voices_to_test)}: {voice}")
        
        automation = CustomFilenameAutomation(
            headless=True,
            output_directory="./voice_samples",
            custom_filename=f"voice_sample_{voice.lower()}"
        )
        
        result = await automation.run_complete_workflow_with_custom_name(
            query=f"This is a sample of the {voice} voice from OpenAI.fm.",
            voice_instructions=f"Voice Affect: Natural. Voice: {voice}",
            voice_name=voice
        )
        
        results.append({
            "voice": voice,
            "success": result["success"],
            "path": result.get("download_path"),
            "error": result.get("error")
        })
        
        if result["success"]:
            print(f"  ✅ {voice}: Success")
        else:
            print(f"  ❌ {voice}: {result['error']}")
        
        # تاخیر بین تست‌ها
        if i < len(voices_to_test) - 1:
            await asyncio.sleep(3)
    
    # خلاصه نتایج
    successful = len([r for r in results if r["success"]])
    print(f"\n📊 Summary: {successful}/{len(results)} voices tested successfully")
    
    return results

async def main():
    """اجرای تمام مثال‌ها"""
    print("🚀 OpenAI.fm Automation Examples")
    print("=" * 70)
    
    try:
        # مثال پایه
        await basic_example()
        
        # تاخیر بین مثال‌ها
        await asyncio.sleep(2)
        
        # مثال نام فایل سفارشی
        await custom_filename_example()
        
        # تاخیر بین مثال‌ها
        await asyncio.sleep(2)
        
        # مثال صداهای مختلف
        await multiple_voices_example()
        
        print("\n" + "=" * 70)
        print("🎉 All examples completed!")
        print("\n💡 Tips:")
        print("  - Files are downloaded with original names first")
        print("  - Then renamed to your custom filename")
        print("  - All files are saved in specified directories")
        print("  - Use headless=False for debugging")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")

if __name__ == "__main__":
    asyncio.run(main())
