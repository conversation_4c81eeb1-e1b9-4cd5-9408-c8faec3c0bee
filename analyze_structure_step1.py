#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرحله 1: بررسی ساختار واقعی "Namazda örtünmə"
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_namazda_ortunme_structure():
    """تحلیل ساختار Namazda örtünmə"""
    print("🔍 مرحله 1: بررسی ساختار واقعی 'Namazda örtünmə'")
    print("=" * 60)
    
    url = "https://www.leader.ir/az/book/246/1?sn=31668"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # 1. بررسی عنوان صفحه
        title_element = soup.find('title')
        if title_element:
            print(f"📄 عنوان صفحه: {title_element.get_text().strip()}")
        
        # 2. بررسی breadcrumb (مسیر ناوبری)
        breadcrumb = soup.find('label')
        if breadcrumb:
            print(f"🗂️ مسیر ناوبری: {breadcrumb.get_text().strip()}")
        
        # 3. جستجوی لینک‌های زیرمجموعه
        print(f"\n🔗 جستجوی لینک‌های زیرمجموعه:")
        links = soup.find_all('a', href=True)
        sub_links = []
        
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            
            if 'sn=' in href and text and len(text) > 5:
                # استخراج sn
                sn_match = re.search(r'sn=(\d+)', href)
                if sn_match:
                    sn = sn_match.group(1)
                    sub_links.append((text, sn, href))
        
        # فیلتر کردن لینک‌های مرتبط
        relevant_links = []
        for text, sn, href in sub_links:
            if any(keyword in text.lower() for keyword in ['paltar', 'şərt', 'müstəhəb', 'məkruh']):
                relevant_links.append((text, sn, href))
                print(f"  ✅ {text} (sn={sn})")
        
        # 4. بررسی آیکون‌های + و doc
        print(f"\n📋 بررسی آیکون‌های + و doc:")
        
        # جستجوی عناصر با کلاس‌های مختلف
        plus_icons = soup.find_all('i', class_='fa fclose pull-right')
        doc_icons = soup.find_all('i', class_='fa doc pull-right')
        
        print(f"  📂 آیکون‌های + (sub_category): {len(plus_icons)}")
        print(f"  📄 آیکون‌های doc (masael): {len(doc_icons)}")
        
        # 5. بررسی محتوای مسائل
        print(f"\n📝 بررسی محتوای مسائل:")
        page_text = soup.get_text()
        
        # جستجوی مسائل 48-56
        found_masael = []
        for num in range(48, 57):
            if f"Məsələ {num}" in page_text:
                found_masael.append(num)
        
        print(f"  📋 مسائل 48-56 یافت شده: {found_masael}")
        
        # نمایش چند مسئله اول
        if found_masael:
            print(f"\n📖 نمونه مسائل:")
            for num in found_masael[:3]:
                pattern = rf'(Məsələ\s+{num}\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                if match:
                    header, text = match.groups()
                    clean_text = text.strip()
                    clean_text = re.sub(r'\s+', ' ', clean_text)
                    print(f"  {header}")
                    print(f"    {clean_text[:100]}...")
        
        return relevant_links
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return []


if __name__ == "__main__":
    links = analyze_namazda_ortunme_structure()
    print(f"\n🎯 خلاصه: {len(links)} زیرمجموعه مرتبط یافت شد")
    for text, sn, href in links:
        print(f"  - {text} (sn={sn})")
