{"page_title": "ChatGPT", "current_url": "https://chatgpt.com/", "has_main_container": true, "input_elements": [{"selector": "div[contenteditable=\"true\"]", "index": 0, "tag_name": "DIV", "placeholder": "", "class": "ProseMirror ProseMirror-focused", "id": "prompt-textarea", "is_visible": true}, {"selector": "textarea", "index": 0, "tag_name": "TEXTAREA", "placeholder": "Ask anything", "class": "text-token-text-primary placeholder:text-token-text-tertiary block h-10 w-full resize-none border-0 bg-transparent px-0 py-2 ring-0 placeholder:ps-px", "id": "", "is_visible": false}], "response_elements": [{"selector": "[role=\"presentation\"]", "index": 0, "class": "composer-parent flex flex-col overflow-hidden focus-visible:outline-0 h-full", "role": "presentation", "is_visible": true}], "button_elements": [{"selector": "button", "index": 0, "text": "ChatGPT", "aria_label": "", "class": "group flex cursor-pointer justify-center items-center gap-1 rounded-lg min-h-9 touch:min-h-10 px-2.5 text-lg hover:bg-token-surface-hover focus-visible:bg-token-surface-hover font-normal whitespace-nowrap focus-visible:outline-none", "is_visible": false, "is_enabled": true}, {"selector": "button", "index": 1, "text": "Log in", "aria_label": "", "class": "btn relative btn-primary btn-small", "is_visible": false, "is_enabled": true}, {"selector": "button", "index": 2, "text": "ChatGPT", "aria_label": "", "class": "group flex cursor-pointer justify-center items-center gap-1 rounded-lg min-h-9 touch:min-h-10 px-2.5 text-lg hover:bg-token-surface-hover focus-visible:bg-token-surface-hover font-normal whitespace-nowrap focus-visible:outline-none", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 3, "text": "Log in", "aria_label": "", "class": "btn relative btn-primary", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 4, "text": "Sign up for free", "aria_label": "", "class": "btn relative btn-secondary screen-arch:hidden md:screen-arch:flex", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 5, "text": "", "aria_label": "Open profile menu", "class": "group user-select-none ps-2 focus-visible:outline-0", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 6, "text": "Attach", "aria_label": "Upload files and more", "class": "flex items-center justify-center h-9 rounded-full border border-token-border-default text-token-text-secondary min-w-8 w-auto p-2 text-[13px] font-semibold radix-state-open:bg-black/10 can-hover:hover:bg-token-main-surface-secondary", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 7, "text": "Search", "aria_label": "Search", "class": "flex h-full min-w-8 items-center justify-center p-2", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 8, "text": "Voice", "aria_label": "Start voice mode", "class": "relative flex h-9 items-center justify-center rounded-full transition-colors disabled:text-gray-50 disabled:opacity-30 min-w-8 p-2 bg-[#00000014] dark:bg-[#FFFFFF29] text-token-text-primary can-hover:hover:bg-[#0000001F] can-hover:hover:dark:bg-[#FFFFFF3D]", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 9, "text": "Manage Cookies", "aria_label": "", "class": "btn relative btn-secondary text-sm", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 10, "text": "Reject non-essential", "aria_label": "", "class": "btn relative btn-secondary text-sm", "is_visible": true, "is_enabled": true}, {"selector": "button", "index": 11, "text": "Accept all", "aria_label": "", "class": "btn relative btn-secondary text-sm", "is_visible": true, "is_enabled": true}], "message_test": {"success": true, "input_selector": "JSHandle@<div translate=\"no\" id=\"prompt-textarea\" contentedita…>…</div>", "send_button_found": true}}