#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست URL صحیح برای "Namazda örtünmə"
"""

import requests
import re
from bs4 import BeautifulSoup


def test_correct_url():
    """تست URL صحیح"""
    print("🔍 تست URL صحیح برای 'Namazda örtünmə'")
    print("=" * 50)
    
    # URL های مختلف برای تست
    urls_to_test = [
        ("URL فعلی", "https://www.leader.ir/az/book/246/1?sn=31668"),
        ("URL جایگزین", "https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31668"),
    ]
    
    for url_name, url in urls_to_test:
        print(f"\n🔍 تست {url_name}:")
        print(f"🌐 URL: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            # جستجوی مسائل 48-56
            found_numbers = []
            for num in range(48, 57):
                if f"Məsələ {num}" in page_text:
                    found_numbers.append(num)
            
            print(f"📋 مسائل 48-56 یافت شده: {found_numbers}")
            
            if found_numbers == list(range(48, 57)):
                print("✅ عالی! تمام مسائل 48-56 یافت شد")
                
                # استخراج نمونه
                pattern_48 = r'(Məsələ\s+48\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                match_48 = re.search(pattern_48, page_text, re.DOTALL | re.IGNORECASE)
                if match_48:
                    header, text = match_48.groups()
                    clean_text = text.strip()
                    clean_text = re.sub(r'\s+', ' ', clean_text)
                    print(f"📝 نمونه - {header}")
                    print(f"   {clean_text[:100]}...")
                
                return url
            else:
                print("❌ مسائل 48-56 کامل یافت نشد")
                
        except Exception as e:
            print(f"❌ خطا: {e}")
    
    return None


if __name__ == "__main__":
    correct_url = test_correct_url()
    if correct_url:
        print(f"\n🎯 URL صحیح: {correct_url}")
    else:
        print(f"\n💥 هیچ URL صحیحی یافت نشد")
