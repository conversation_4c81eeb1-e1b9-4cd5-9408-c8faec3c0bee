#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بررسی صفحه "Namazda örtünmə" برای درک ساختار sub_category ها
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_namazda_ortunme():
    """تحلیل صفحه Namazda örtünmə"""
    print("🔍 تحلیل صفحه 'Namazda örtünmə'")
    
    url = "https://www.leader.ir/az/book/246/1?sn=31668"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # جستجوی عناصر strong که شامل "Məsələ" هستند
        print("\n🔍 جستجوی مسائل:")
        strong_elements = soup.find_all('strong')
        masael_count = 0
        masael_numbers = []
        
        for strong in strong_elements:
            text = strong.get_text().strip()
            if re.match(r'Məsələ\s+\d+\.', text, re.IGNORECASE):
                masael_count += 1
                number_match = re.search(r'Məsələ\s+(\d+)\.', text)
                if number_match:
                    number = int(number_match.group(1))
                    masael_numbers.append(number)
                    print(f"  {masael_count}. {text} (شماره: {number})")
                    
                    # نمایش محتوای کوتاه
                    parent = strong.parent
                    if parent:
                        full_text = parent.get_text()
                        content = full_text.replace(text, '', 1).strip()
                        print(f"     محتوا: {content[:80]}...")
        
        print(f"\n📊 تعداد مسائل یافت شده: {masael_count}")
        print(f"📋 شماره مسائل: {sorted(masael_numbers)}")
        
        # بررسی محدوده مسائل 48-56
        target_range_48_56 = list(range(48, 57))  # 48 تا 56
        found_48_56 = [n for n in masael_numbers if n in target_range_48_56]
        print(f"\n🎯 مسائل 48-56 یافت شده: {found_48_56}")
        
        # بررسی محدوده مسائل 99-100
        target_range_99_100 = list(range(99, 101))  # 99 تا 100
        found_99_100 = [n for n in masael_numbers if n in target_range_99_100]
        print(f"🎯 مسائل 99-100 یافت شده: {found_99_100}")
        
        # جستجوی لینک‌های زیرمجموعه
        print(f"\n🔗 جستجوی لینک‌های زیرمجموعه:")
        links = soup.find_all('a', href=True)
        sub_links = []
        
        for link in links:
            href = link.get('href', '')
            text = link.get_text().strip()
            
            if 'sn=' in href and text and len(text) > 5:
                # استخراج sn
                sn_match = re.search(r'sn=(\d+)', href)
                if sn_match:
                    sn = sn_match.group(1)
                    sub_links.append((text, sn, href))
        
        # فیلتر کردن لینک‌های مرتبط
        relevant_links = []
        for text, sn, href in sub_links:
            if any(keyword in text.lower() for keyword in ['paltarının', 'şərtləri', 'müstəhəb', 'məkruh']):
                relevant_links.append((text, sn, href))
                print(f"  ✅ {text} (sn={sn})")
        
        print(f"\n📊 تعداد زیرمجموعه‌های مرتبط: {len(relevant_links)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return False


if __name__ == "__main__":
    analyze_namazda_ortunme()
