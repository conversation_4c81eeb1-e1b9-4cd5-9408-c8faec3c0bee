#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست الگوریتم جدید برای استخراج مسائل خاص هر masael
"""

import sys
sys.path.append('.')
from crawler import LeaderCrawler


def test_specific_masael_extraction():
    """تست استخراج مسائل خاص"""
    print("🚀 تست الگوریتم جدید برای مسائل خاص")
    print("=" * 50)
    
    crawler = LeaderCrawler()
    
    # تست با چند masael مختلف
    test_items = [
        {
            'id': '31659',
            'title': 'Vacib namazlar',
            'type': 'masael',
            'url': 'https://www.leader.ir/az/book/246/1?sn=31659',
            'expected_masael': [1]  # انتظار: فقط مسئله 1
        },
        {
            'id': '31660',
            'title': '<PERSON><PERSON><PERSON>əlik vacib namazlar',
            'type': 'masael',
            'url': 'https://www.leader.ir/az/book/246/1?sn=31660',
            'expected_masael': [2, 3]  # انتظار: مسائل 2 و 3
        },
        {
            'id': '32911',
            'title': 'Sübh namazının vaxtı',
            'type': 'masael',
            'url': 'https://www.leader.ir/az/book/246/1?sn=32911',
            'expected_masael': [4, 5]  # انتظار: مسائل 4 و 5
        }
    ]
    
    for i, test_item in enumerate(test_items, 1):
        print(f"\n{'='*40}")
        print(f"🔍 تست {i}: {test_item['title']}")
        print(f"🌐 URL: {test_item['url']}")
        print(f"📋 انتظار: مسائل {test_item['expected_masael']}")
        
        # استخراج محتوا
        crawler.extract_masael_content(test_item)
        
        # بررسی نتایج
        if test_item.get('masael_contents'):
            contents = test_item['masael_contents']
            print(f"\n✅ نتایج:")
            print(f"📊 تعداد مسائل استخراج شده: {len(contents)}")
            
            # نمایش تمام مسائل استخراج شده
            extracted_numbers = []
            for j, masael in enumerate(contents, 1):
                print(f"\n{j}. {masael['header']}")
                print(f"   متن: {masael['text'][:100]}...")
                
                # استخراج شماره مسئله
                import re
                number_match = re.search(r'Məsələ\s+(\d+)\.', masael['header'])
                if number_match:
                    extracted_numbers.append(int(number_match.group(1)))
            
            # مقایسه با انتظارات
            expected = test_item['expected_masael']
            if extracted_numbers == expected:
                print(f"\n✅ موفق! مسائل استخراج شده: {extracted_numbers}")
            else:
                print(f"\n❌ ناموفق!")
                print(f"   انتظار: {expected}")
                print(f"   دریافت: {extracted_numbers}")
        
        else:
            print("\n❌ هیچ محتوایی استخراج نشد!")
    
    print(f"\n{'='*50}")
    print("🎯 خلاصه تست:")
    print("این تست باید نشان دهد که:")
    print("- 'Vacib namazlar' فقط مسئله 1 دارد")
    print("- 'Gündəlik vacib namazlar' مسائل 2 و 3 دارد")
    print("- 'Sübh namazının vaxtı' مسائل 4 و 5 دارد")


if __name__ == "__main__":
    test_specific_masael_extraction()
