#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرحله 4: جستجوی مسئله 57
"""

import requests
import re
from bs4 import BeautifulSoup


def search_masael_57():
    """جستجوی مسئله 57 در صفحات مختلف"""
    print("🔍 مرحله 4: جستجوی مسئله 57")
    print("=" * 60)
    
    # صفحات مختلف برای بررسی
    pages_to_check = [
        ("Namazda örtünmə", "https://www.leader.ir/az/book/246/1?sn=31668"),
        ("Namaz paltarının şərtləri", "https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31669"),
        ("Namaz paltarına aid müstəhəb və məkruh işlər", "https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31677"),
    ]
    
    # محدوده sn ها برای بررسی (اطراف 31669)
    base_url = "https://www.leader.ir/az/book/246/1?sn="
    sn_range = range(31669, 31678)  # 31669 تا 31677
    
    for sn in sn_range:
        url = f"{base_url}{sn}"
        page_name = f"sn={sn}"
        pages_to_check.append((page_name, url))
    
    for page_name, url in pages_to_check:
        print(f"\n🔍 بررسی صفحه: {page_name}")
        print(f"🌐 URL: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                page_text = soup.get_text()
                
                # جستجوی مسئله 57
                if "Məsələ 57" in page_text:
                    print(f"✅ مسئله 57 در {page_name} یافت شد!")
                    
                    # استخراج محتوای مسئله 57
                    pattern_57 = r'(Məsələ\s+57\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                    match_57 = re.search(pattern_57, page_text, re.DOTALL | re.IGNORECASE)
                    if match_57:
                        header, text = match_57.groups()
                        clean_text = text.strip()
                        clean_text = re.sub(r'\s+', ' ', clean_text)
                        print(f"📝 {header}")
                        print(f"   {clean_text[:200]}...")
                    
                    # بررسی مسائل اطراف
                    found_numbers = []
                    for num in range(55, 65):  # 55 تا 64
                        if f"Məsələ {num}" in page_text:
                            found_numbers.append(num)
                    
                    print(f"📋 مسائل 55-64 یافت شده: {found_numbers}")
                    
                    return sn, url
                else:
                    print(f"❌ مسئله 57 یافت نشد")
            else:
                print(f"❌ خطا {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطا: {e}")
    
    print(f"\n💥 مسئله 57 در هیچ‌کدام از صفحات یافت نشد!")
    return None, None


if __name__ == "__main__":
    sn, url = search_masael_57()
    if sn:
        print(f"\n🎯 URL صحیح مسئله 57: {url}")
    else:
        print(f"\n❓ مسئله 57 ممکن است در بخش دیگری باشد")
