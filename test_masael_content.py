#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست استخراج محتوای masael
"""

import requests
import re
from bs4 import BeautifulSoup


def test_masael_content():
    """تست استخراج محتوای یک masael"""
    print("🔍 تست استخراج محتوای masael")
    
    # تست با "Vacib namazlar" (sn=31659)
    url = "https://www.leader.ir/az/book/246/1?sn=31659"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # جستجوی متن‌های "Məsələ"
        page_text = soup.get_text()
        
        # الگوی regex برای پیدا کردن مسائل
        masael_pattern = re.compile(r'(<PERSON><PERSON>sələ\s+\d+\..*?)(?=Məsələ\s+\d+\.|$)', re.DOTALL | re.IGNORECASE)
        
        matches = masael_pattern.findall(page_text)
        
        print(f"📊 تعداد مسائل یافت شده: {len(matches)}")
        
        for i, match in enumerate(matches[:3], 1):  # نمایش 3 مورد اول
            # تمیز کردن متن
            clean_text = re.sub(r'\s+', ' ', match.strip())
            
            # جدا کردن header و text
            header_match = re.match(r'(Məsələ\s+\d+\.)\s*(.*)', clean_text, re.IGNORECASE)
            
            if header_match:
                header = header_match.group(1).strip()
                text = header_match.group(2).strip()
                
                print(f"\n📝 مسئله {i}:")
                print(f"  Header: {header}")
                print(f"  Text: {text[:100]}..." if len(text) > 100 else f"  Text: {text}")
            else:
                print(f"\n📝 مسئله {i}: {clean_text[:100]}...")
        
        return matches
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return []


def test_with_beautiful_soup():
    """تست با استفاده از BeautifulSoup برای پیدا کردن عناصر دقیق‌تر"""
    print("\n🔍 تست با BeautifulSoup")
    
    url = "https://www.leader.ir/az/book/246/1?sn=31659"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # جستجوی عناصر که شامل "Məsələ" هستند
        masael_elements = soup.find_all(text=re.compile(r'Məsələ\s+\d+\.', re.IGNORECASE))
        
        print(f"📊 تعداد عناصر یافت شده: {len(masael_elements)}")
        
        for i, element in enumerate(masael_elements[:3], 1):
            parent = element.parent
            if parent:
                parent_text = parent.get_text().strip()
                clean_text = re.sub(r'\s+', ' ', parent_text)
                
                print(f"\n📝 عنصر {i}:")
                print(f"  Tag: {parent.name}")
                print(f"  Text: {clean_text[:150]}..." if len(clean_text) > 150 else f"  Text: {clean_text}")
        
    except Exception as e:
        print(f"❌ خطا: {e}")


if __name__ == "__main__":
    test_masael_content()
    test_with_beautiful_soup()
