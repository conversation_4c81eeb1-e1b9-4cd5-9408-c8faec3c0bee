#!/usr/bin/env python3
"""
ChatGPT Website Structure Analyzer
تحلیلگر ساختار وب‌سایت ChatGPT
"""

import asyncio
import json
from playwright.async_api import async_playwright
from loguru import logger

class ChatGPTAnalyzer:
    """تحلیلگر ساختار وب‌سایت ChatGPT"""
    
    def __init__(self):
        self.url = "https://chatgpt.com/"
        self.analysis_results = {}
        
    async def analyze_website(self):
        """تجزیه و تحلیل کامل وب‌سایت ChatGPT"""
        logger.info("🔍 شروع تجزیه و تحلیل وب‌سایت ChatGPT...")
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)  # نمایش برای تجزیه و تحلیل
            context = await browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            )
            page = await context.new_page()
            
            try:
                # رفتن به صفحه اصلی
                logger.info(f"📍 رفتن به {self.url}")
                await page.goto(self.url, wait_until="networkidle")
                await asyncio.sleep(3)
                
                # تجزیه و تحلیل عناصر اصلی
                await self._analyze_main_elements(page)
                
                # تجزیه و تحلیل فرم ورودی
                await self._analyze_input_elements(page)
                
                # تجزیه و تحلیل منطقه پاسخ
                await self._analyze_response_area(page)
                
                # تجزیه و تحلیل دکمه‌ها و کنترل‌ها
                await self._analyze_controls(page)
                
                # تست ارسال پیام
                await self._test_message_sending(page)
                
                # ذخیره نتایج
                await self._save_analysis_results()
                
            except Exception as e:
                logger.error(f"❌ خطا در تجزیه و تحلیل: {e}")
                
            finally:
                await browser.close()
                
    async def _analyze_main_elements(self, page):
        """تجزیه و تحلیل عناصر اصلی صفحه"""
        logger.info("🔍 تجزیه و تحلیل عناصر اصلی...")
        
        # بررسی title صفحه
        title = await page.title()
        self.analysis_results["page_title"] = title
        logger.info(f"📄 عنوان صفحه: {title}")
        
        # بررسی URL فعلی
        current_url = page.url
        self.analysis_results["current_url"] = current_url
        logger.info(f"🔗 URL فعلی: {current_url}")
        
        # بررسی وجود عناصر اصلی
        main_container = await page.query_selector("main")
        if main_container:
            logger.info("✅ عنصر main پیدا شد")
            self.analysis_results["has_main_container"] = True
        else:
            logger.warning("⚠️ عنصر main پیدا نشد")
            self.analysis_results["has_main_container"] = False
            
    async def _analyze_input_elements(self, page):
        """تجزیه و تحلیل عناصر ورودی"""
        logger.info("🔍 تجزیه و تحلیل عناصر ورودی...")
        
        input_selectors = [
            'textarea[placeholder*="Message"]',
            'textarea[data-id="root"]',
            'div[contenteditable="true"]',
            'textarea',
            'input[type="text"]',
            '[role="textbox"]'
        ]
        
        found_inputs = []
        
        for selector in input_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        # دریافت اطلاعات عنصر
                        tag_name = await element.evaluate("el => el.tagName")
                        placeholder = await element.get_attribute("placeholder") or ""
                        class_name = await element.get_attribute("class") or ""
                        id_attr = await element.get_attribute("id") or ""
                        
                        input_info = {
                            "selector": selector,
                            "index": i,
                            "tag_name": tag_name,
                            "placeholder": placeholder,
                            "class": class_name,
                            "id": id_attr,
                            "is_visible": await element.is_visible()
                        }
                        
                        found_inputs.append(input_info)
                        logger.info(f"📝 ورودی پیدا شد: {selector} - {placeholder}")
                        
            except Exception as e:
                logger.debug(f"خطا در بررسی {selector}: {e}")
                
        self.analysis_results["input_elements"] = found_inputs
        
    async def _analyze_response_area(self, page):
        """تجزیه و تحلیل منطقه نمایش پاسخ"""
        logger.info("🔍 تجزیه و تحلیل منطقه پاسخ...")
        
        response_selectors = [
            '[data-message-author-role="assistant"]',
            '.markdown',
            '[role="presentation"]',
            '.prose',
            '.message',
            '.response'
        ]
        
        found_responses = []
        
        for selector in response_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        class_name = await element.get_attribute("class") or ""
                        role = await element.get_attribute("role") or ""
                        
                        response_info = {
                            "selector": selector,
                            "index": i,
                            "class": class_name,
                            "role": role,
                            "is_visible": await element.is_visible()
                        }
                        
                        found_responses.append(response_info)
                        logger.info(f"💬 منطقه پاسخ پیدا شد: {selector}")
                        
            except Exception as e:
                logger.debug(f"خطا در بررسی {selector}: {e}")
                
        self.analysis_results["response_elements"] = found_responses
        
    async def _analyze_controls(self, page):
        """تجزیه و تحلیل دکمه‌ها و کنترل‌ها"""
        logger.info("🔍 تجزیه و تحلیل کنترل‌ها...")
        
        button_selectors = [
            'button[data-testid="send-button"]',
            'button[aria-label*="Send"]',
            'button:has-text("Send")',
            '[role="button"]',
            'button'
        ]
        
        found_buttons = []
        
        for selector in button_selectors:
            try:
                elements = await page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        text_content = await element.text_content() or ""
                        aria_label = await element.get_attribute("aria-label") or ""
                        class_name = await element.get_attribute("class") or ""
                        
                        button_info = {
                            "selector": selector,
                            "index": i,
                            "text": text_content.strip(),
                            "aria_label": aria_label,
                            "class": class_name,
                            "is_visible": await element.is_visible(),
                            "is_enabled": await element.is_enabled()
                        }
                        
                        found_buttons.append(button_info)
                        logger.info(f"🔘 دکمه پیدا شد: {text_content.strip()} - {aria_label}")
                        
            except Exception as e:
                logger.debug(f"خطا در بررسی {selector}: {e}")
                
        self.analysis_results["button_elements"] = found_buttons
        
    async def _test_message_sending(self, page):
        """تست ارسال پیام"""
        logger.info("🧪 تست ارسال پیام...")
        
        try:
            # پیدا کردن فیلد ورودی
            input_element = None
            test_selectors = [
                'textarea[placeholder*="Message"]',
                'div[contenteditable="true"]',
                'textarea',
                '[role="textbox"]'
            ]
            
            for selector in test_selectors:
                try:
                    element = await page.wait_for_selector(selector, timeout=5000)
                    if element and await element.is_visible():
                        input_element = element
                        logger.info(f"✅ فیلد ورودی پیدا شد: {selector}")
                        break
                except:
                    continue
                    
            if not input_element:
                logger.error("❌ فیلد ورودی پیدا نشد")
                self.analysis_results["message_test"] = {"success": False, "error": "Input field not found"}
                return
                
            # تست تایپ کردن
            test_message = "Hello, this is a test message."
            await input_element.click()
            await asyncio.sleep(1)
            await input_element.fill(test_message)
            await asyncio.sleep(2)
            
            # پیدا کردن دکمه ارسال
            send_button = None
            send_selectors = [
                'button[data-testid="send-button"]',
                'button[aria-label*="Send"]',
                'button:has-text("Send")'
            ]
            
            for selector in send_selectors:
                try:
                    element = await page.query_selector(selector)
                    if element and await element.is_visible() and await element.is_enabled():
                        send_button = element
                        logger.info(f"✅ دکمه ارسال پیدا شد: {selector}")
                        break
                except:
                    continue
                    
            if send_button:
                # کلیک روی دکمه ارسال (فقط برای تست - بدون ارسال واقعی)
                logger.info("🔘 دکمه ارسال آماده است")
                self.analysis_results["message_test"] = {
                    "success": True,
                    "input_selector": str(input_element),
                    "send_button_found": True
                }
            else:
                logger.warning("⚠️ دکمه ارسال پیدا نشد")
                self.analysis_results["message_test"] = {
                    "success": False,
                    "input_selector": str(input_element),
                    "send_button_found": False
                }
                
        except Exception as e:
            logger.error(f"❌ خطا در تست ارسال پیام: {e}")
            self.analysis_results["message_test"] = {"success": False, "error": str(e)}
            
    async def _save_analysis_results(self):
        """ذخیره نتایج تجزیه و تحلیل"""
        logger.info("💾 ذخیره نتایج تجزیه و تحلیل...")
        
        with open("chatgpt_analysis_results.json", "w", encoding="utf-8") as f:
            json.dump(self.analysis_results, f, indent=2, ensure_ascii=False)
            
        logger.info("✅ نتایج در chatgpt_analysis_results.json ذخیره شد")
        
        # نمایش خلاصه
        self._print_summary()
        
    def _print_summary(self):
        """نمایش خلاصه نتایج"""
        logger.info("\n" + "="*50)
        logger.info("📊 خلاصه تجزیه و تحلیل ChatGPT")
        logger.info("="*50)
        
        logger.info(f"📄 عنوان صفحه: {self.analysis_results.get('page_title', 'نامشخص')}")
        logger.info(f"🔗 URL: {self.analysis_results.get('current_url', 'نامشخص')}")
        
        input_count = len(self.analysis_results.get('input_elements', []))
        logger.info(f"📝 تعداد عناصر ورودی: {input_count}")
        
        response_count = len(self.analysis_results.get('response_elements', []))
        logger.info(f"💬 تعداد عناصر پاسخ: {response_count}")
        
        button_count = len(self.analysis_results.get('button_elements', []))
        logger.info(f"🔘 تعداد دکمه‌ها: {button_count}")
        
        test_result = self.analysis_results.get('message_test', {})
        if test_result.get('success'):
            logger.info("✅ تست ارسال پیام: موفق")
        else:
            logger.info("❌ تست ارسال پیام: ناموفق")
            
        logger.info("="*50)

async def main():
    """اجرای تحلیلگر"""
    analyzer = ChatGPTAnalyzer()
    await analyzer.analyze_website()

if __name__ == "__main__":
    asyncio.run(main())
