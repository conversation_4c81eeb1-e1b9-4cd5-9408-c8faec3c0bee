#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست پارس کردن HTML ارائه شده برای "Namazda örtünmə"
"""

import re
from bs4 import BeautifulSoup


def test_html_parsing():
    """تست پارس کردن HTML"""
    print("🔍 تست پارس کردن HTML برای 'Namazda örtünmə'")
    print("=" * 50)
    
    # HTML ارائه شده
    html_content = '''
<h5 class="matn" style="text-align:justify"><strong>Məsələ 48. </strong>Vacib namazlarda və bu namazlara aid hissələrdə, məsələn, ehtiyat namazı və ya unudulan səcdə və təşəhhüddə, həmçinin ehtiyat-vacibə görə, səhv-səcdəsində bədən örtülü olmalıdır.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 49. </strong>Namazda örtülü olmağın vacibliyi mükəlləfin namaz qıldığı məkanda naməhrəmin olmasından asılı deyildir. Əksinə, həmin məkanda heç kim olmasa belə, örtülü olmaq namazın düzgünlüyü şərtidir.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 50. </strong>Kişi namaz qılanda, onu heç kim görməsə belə, öz övrətini (qabağını və arxasını) örtməlidir. Daha yaxşı olar ki, göbəkdən dizlərə qədər olan nahiyəni örtsün.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 51. </strong>Qadın namaz qılanda bütün bədənini və saçlarını örtməlidir. Amma dəstəmazda yuyulması vacib olan ölçüdə üzü örtmək, həmçinin biləyə qədər əlləri və oynağa qədər ayaqları örtmək vacib deyildir. Amma naməhrəm onu gördüyü təqdirdə, oynağa qədər ayaqlarını da örtməlidir.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 52. </strong>Şəri hökmü bilmədiyinə görə namazda lazımi hüdudda örtünməyən şəxsin namazı düzgün deyildir. Amma əgər bu şəxs qafil və ya qasir cahil olarsa, yəni örtünmədən namazın düzgün olmayacağını hətta ehtimal belə etməzsə, bu halda namaz düzgündür.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 53. </strong>Çənə üzün bir hissəsidir və namaz qılanda çənəni örtmək qadına vacib deyildir. Amma çənənin altı üzün bir hissəsi sayılmır və həmin nahiyəni örtmək vacibdir.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 54. </strong>Meyit namazında örtülü olmaq vacib deyildir, amma örtülü olmaq ehtiyat-müstəhəbə müvafiqdir.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 55. </strong>Vacib namazlarda olduğu kimi, müstəhəb namazlarda da örtülü olmaq namazın düzgünlüyü şərtidir.</h5>

<h5 class="matn" style="text-align:justify"><strong>Məsələ 56. </strong>Əgər bir şəxs namaz əsnasında lazımi həddə örtülü olmadığını başa düşsə, ehtiyata görə, namazı başa çatdırmalı və onu yenidən qılmalıdır. Amma əgər özünü dərhal örtərsə, namazının düzgün olması uzaq ehtimal deyildir. Həmçinin əgər namazı qılıb qurtardıqdan sonra namazda lazımi həddə örtünmədiyini başa düşsə, onun namazı düzgündür.</h5>
'''
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # استخراج مسائل با regex
    page_text = soup.get_text()
    
    masael_pattern = re.compile(
        r'(Məsələ\s+\d+\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)',
        re.DOTALL | re.IGNORECASE
    )
    
    matches = masael_pattern.findall(page_text)
    
    print(f"📊 تعداد مسائل یافت شده: {len(matches)}")
    
    extracted_numbers = []
    for i, (header, text) in enumerate(matches, 1):
        # استخراج شماره
        number_match = re.search(r'Məsələ\s+(\d+)\.', header)
        if number_match:
            number = int(number_match.group(1))
            extracted_numbers.append(number)
        
        # تمیز کردن متن
        clean_text = text.strip()
        clean_text = re.sub(r'\s+', ' ', clean_text)
        
        print(f"{i}. {header}")
        print(f"   شماره: {number}")
        print(f"   متن: {clean_text[:100]}...")
        print()
    
    print(f"📋 شماره مسائل استخراج شده: {sorted(extracted_numbers)}")
    
    expected_numbers = list(range(48, 57))  # 48 تا 56
    print(f"📋 مسائل مورد انتظار: {expected_numbers}")
    
    if sorted(extracted_numbers) == expected_numbers:
        print("✅ عالی! تمام مسائل 48-56 با موفقیت استخراج شد!")
        return True
    else:
        print("❌ مسائل استخراج شده با انتظارات مطابقت ندارد")
        return False


if __name__ == "__main__":
    success = test_html_parsing()
    if success:
        print(f"\n🎉 HTML پارسینگ موفق!")
    else:
        print(f"\n💥 مشکل در HTML پارسینگ")
