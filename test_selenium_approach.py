#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست استفاده از Selenium برای دریافت محتوای dynamic
"""

try:
    from selenium import webdriver
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    import time
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False

import requests
from bs4 import BeautifulSoup


def test_selenium_approach():
    """تست استفاده از Selenium"""
    print("🔍 تست استفاده از Selenium برای محتوای dynamic")
    print("=" * 60)
    
    if not SELENIUM_AVAILABLE:
        print("❌ Selenium در دسترس نیست. نصب کنید:")
        print("pip install selenium")
        return False
    
    # تنظیمات Chrome
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # اجرا بدون نمایش
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    
    try:
        # راه‌اندازی WebDriver
        print("🚀 راه‌اندازی Chrome WebDriver...")
        driver = webdriver.Chrome(options=chrome_options)
        
        # رفتن به صفحه اصلی
        print("🌐 بارگذاری صفحه اصلی...")
        driver.get("https://www.leader.ir/az/book/246/1")
        
        # انتظار برای بارگذاری کامل
        time.sleep(3)
        
        # جستجوی عنصر "Namazda örtünmə"
        print("🔍 جستجوی عنصر 'Namazda örtünmə'...")
        
        try:
            # جستجوی لینک با عنوان "Namazda örtünmə"
            namazda_ortunme_element = driver.find_element(
                By.XPATH, 
                "//a[@title='Namazda örtünmə'] | //span[contains(text(), 'Namazda örtünmə')]"
            )
            print("✅ عنصر 'Namazda örtünmə' یافت شد!")
            
            # کلیک روی عنصر
            print("👆 کلیک روی 'Namazda örtünmə'...")
            driver.execute_script("arguments[0].click();", namazda_ortunme_element)
            
            # انتظار برای بارگذاری محتوا
            time.sleep(3)
            
            # دریافت محتوای صفحه بعد از کلیک
            page_source = driver.page_source
            
            # بررسی وجود مسائل 48-56
            found_masael = []
            for num in range(48, 57):
                if f"Məsələ {num}" in page_source:
                    found_masael.append(num)
            
            print(f"📋 مسائل 48-56 یافت شده: {found_masael}")
            
            if found_masael:
                print("🎉 موفق! محتوای dynamic با Selenium دریافت شد!")
                
                # استخراج نمونه محتوا
                soup = BeautifulSoup(page_source, 'html.parser')
                
                # جستجوی مسئله 48
                import re
                pattern_48 = r'(Məsələ\s+48\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                match_48 = re.search(pattern_48, page_source, re.DOTALL | re.IGNORECASE)
                if match_48:
                    header, text = match_48.groups()
                    clean_text = text.strip()
                    clean_text = re.sub(r'\s+', ' ', clean_text)
                    print(f"📝 نمونه - {header}")
                    print(f"   {clean_text[:100]}...")
                
                return True
            else:
                print("❌ مسائل 48-56 یافت نشد")
                
                # نمایش بخشی از محتوا برای دیباگ
                print("🔍 نمونه محتوای دریافت شده:")
                print(page_source[:500] + "...")
                
                return False
                
        except Exception as e:
            print(f"❌ خطا در یافتن عنصر: {e}")
            return False
            
    except Exception as e:
        print(f"❌ خطا در راه‌اندازی Selenium: {e}")
        return False
        
    finally:
        try:
            driver.quit()
            print("🔚 WebDriver بسته شد")
        except:
            pass


def test_alternative_approach():
    """تست روش جایگزین بدون Selenium"""
    print("\n🔄 تست روش جایگزین بدون Selenium")
    print("=" * 60)
    
    # استفاده از HTML ثابت که کاربر ارائه داده
    print("📄 استفاده از HTML ثابت ارائه شده توسط کاربر...")
    
    # HTML ارائه شده توسط کاربر
    html_content = '''
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 48. </strong>Vacib namazlarda və bu namazlara aid hissələrdə, məsələn, ehtiyat namazı və ya unudulan səcdə və təşəhhüddə, həmçinin ehtiyat-vacibə görə, səhv-səcdəsində bədən örtülü olmalıdır.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 49. </strong>Namazda örtülü olmağın vacibliyi mükəlləfin namaz qıldığı məkanda naməhrəmin olmasından asılı deyildir. Əksinə, həmin məkanda heç kim olmasa belə, örtülü olmaq namazın düzgünlüyü şərtidir.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 50. </strong>Kişi namaz qılanda, onu heç kim görməsə belə, öz övrətini (qabağını və arxasını) örtməlidir. Daha yaxşı olar ki, göbəkdən dizlərə qədər olan nahiyəni örtsün.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 51. </strong>Qadın namaz qılanda bütün bədənini və saçlarını örtməlidir. Amma dəstəmazda yuyulması vacib olan ölçüdə üzü örtmək, həmçinin biləyə qədər əlləri və oynağa qədər ayaqları örtmək vacib deyildir. Amma naməhrəm onu gördüyü təqdirdə, oynağa qədər ayaqlarını da örtməlidir.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 52. </strong>Şəri hökmü bilmədiyinə görə namazda lazımi hüdudda örtünməyən şəxsin namazı düzgün deyildir. Amma əgər bu şəxs qafil və ya qasir cahil olarsa, yəni örtünmədən namazın düzgün olmayacağını hətta ehtimal belə etməzsə, bu halda namaz düzgündür.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 53. </strong>Çənə üzün bir hissəsidir və namaz qılanda çənəni örtmək qadına vacib deyildir. Amma çənənin altı üzün bir hissəsi sayılmır və həmin nahiyəni örtmək vacibdir.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 54. </strong>Meyit namazında örtülü olmaq vacib deyildir, amma örtülü olmaq ehtiyat-müstəhəbə müvafiqdir.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 55. </strong>Vacib namazlarda olduğu kimi, müstəhəb namazlarda da örtülü olmaq namazın düzgünlüyü şərtidir.</h5>
    <h5 class="matn" style="text-align:justify"><strong>Məsələ 56. </strong>Əgər bir şəxs namaz əsnasında lazımi həddə örtülü olmadığını başa düşsə, ehtiyata görə, namazı başa çatdırmalı və onu yenidən qılmalıdır. Amma əgər özünü dərhal örtərsə, namazının düzgün olması uzaq ehtimal deyildir. Həmçinin əgər namazı qılıb qurtardıqdan sonra namazda lazımi həddə örtünmədiyini başa düşsə, onun namazı düzgündür.</h5>
    '''
    
    # بررسی محتوا
    found_masael = []
    for num in range(48, 57):
        if f"Məsələ {num}" in html_content:
            found_masael.append(num)
    
    print(f"📋 مسائل 48-56 یافت شده: {found_masael}")
    
    if len(found_masael) == 9:
        print("✅ تمام مسائل 48-56 در HTML ثابت موجود است!")
        return True
    else:
        print("❌ برخی مسائل در HTML ثابت موجود نیست")
        return False


if __name__ == "__main__":
    # تست Selenium
    selenium_success = test_selenium_approach()
    
    # تست روش جایگزین
    alternative_success = test_alternative_approach()
    
    print(f"\n🎯 خلاصه نتایج:")
    print(f"  🤖 Selenium: {'✅ موفق' if selenium_success else '❌ ناموفق'}")
    print(f"  📄 HTML ثابت: {'✅ موفق' if alternative_success else '❌ ناموفق'}")
    
    if alternative_success:
        print(f"\n💡 توصیه: استفاده از HTML ثابت برای موارد خاص")
