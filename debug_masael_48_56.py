#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جستجوی مسائل 48-56 در صفحات مختلف
"""

import requests
import re
from bs4 import BeautifulSoup


def search_masael_48_56():
    """جستجوی مسائل 48-56 در صفحات مختلف"""
    print("🔍 جستجوی مسائل 48-56 در صفحات مختلف")
    print("=" * 50)
    
    # صفحات مختلف برای بررسی
    pages_to_check = [
        ("Namazda örtünmə", "https://www.leader.ir/az/book/246/1?sn=31668"),
        ("Namaz paltarının şərtləri", "https://www.leader.ir/az/book/246/1?sn=31669"),
        ("صفحه اصلی NAMAZ", "https://www.leader.ir/az/book/246/1?sn=31658"),
    ]
    
    target_numbers = list(range(48, 57))  # 48 تا 56
    
    for page_name, url in pages_to_check:
        print(f"\n🔍 بررسی صفحه: {page_name}")
        print(f"🌐 URL: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            page_text = soup.get_text()
            
            # جستجوی مسائل 48-56
            found_numbers = []
            for num in target_numbers:
                pattern = rf'Məsələ\s+{num}\.'
                if re.search(pattern, page_text, re.IGNORECASE):
                    found_numbers.append(num)
            
            print(f"📋 مسائل 48-56 یافت شده: {found_numbers}")
            
            if found_numbers:
                print("✅ مسائل یافت شد!")
                
                # نمایش چند نمونه
                for num in found_numbers[:3]:
                    pattern = rf'(Məsələ\s+{num}\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                    match = re.search(pattern, page_text, re.DOTALL | re.IGNORECASE)
                    if match:
                        header, text = match.groups()
                        clean_text = text.strip()
                        clean_text = re.sub(r'\s+', ' ', clean_text)
                        print(f"  📝 {header}")
                        print(f"     {clean_text[:100]}...")
            else:
                print("❌ مسائل یافت نشد")
                
        except Exception as e:
            print(f"❌ خطا در دریافت {url}: {e}")
    
    print(f"\n🎯 خلاصه: مسائل 48-56 باید در کدام صفحه باشند؟")


if __name__ == "__main__":
    search_masael_48_56()
