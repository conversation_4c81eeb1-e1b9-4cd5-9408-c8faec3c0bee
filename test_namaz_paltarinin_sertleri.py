#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تست ساختار جدید "Namaz paltarının şərtləri"
"""

from crawler import LeaderCrawler


def test_namaz_paltarinin_sertleri():
    """تست ساختار جدید Namaz paltarının şərtləri"""
    print("🚀 تست ساختار جدید 'Namaz paltarının şərtləri'")
    print("=" * 60)
    
    crawler = LeaderCrawler()
    
    # شبیه‌سازی آیتم "Namaz paltarının şərtləri" به عنوان sub_category
    namaz_paltarinin_sertleri_item = {
        'id': '31669',
        'title': 'Namaz paltarının şərtləri',
        'type': 'sub_category',
        'url': 'https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31669',
        'children': []
    }
    
    print(f"📂 آیتم اصلی: {namaz_paltarinin_sertleri_item['title']} (نوع: {namaz_paltarinin_sertleri_item['type']})")
    
    # استخراج زیرمجموعه‌ها با روش جدید
    print(f"\n🔍 استخراج زیرمجموعه‌ها با روش جدید...")
    try:
        crawler.extract_deep_subcategories(namaz_paltarinin_sertleri_item)
        
        print(f"✅ استخراج موفق!")
        print(f"📊 تعداد زیرمجموعه‌ها: {len(namaz_paltarinin_sertleri_item['children'])}")
        
        # نمایش زیرمجموعه‌ها
        for i, child in enumerate(namaz_paltarinin_sertleri_item['children'], 1):
            print(f"  {i}. {child['title']} (نوع: {child['type']}, ID: {child['id']})")
            
            # اگر masael است، محتوا را استخراج کن
            if child['type'] == 'masael':
                print(f"    📖 استخراج محتوای masael...")
                crawler.extract_masael_content(child)
                
                if child.get('masael_contents'):
                    print(f"    ✅ {len(child['masael_contents'])} مسئله استخراج شد")
                    
                    # نمایش مسائل
                    for j, masael in enumerate(child['masael_contents'], 1):
                        print(f"      {j}. {masael['header']} - {masael['text'][:50]}...")
                        
                        # بررسی خاص مسئله 57
                        if 'Məsələ 57' in masael['header']:
                            print(f"        🎯 مسئله 57 یافت شد!")
                            print(f"        📝 متن کامل: {masael['text']}")
                else:
                    print(f"    ❌ هیچ مسئله‌ای استخراج نشد")
            
            # اگر sub_category است، نمایش کوتاه
            elif child['type'] == 'sub_category':
                print(f"    📂 زیرمجموعه: {child['title']}")
        
        # خلاصه نهایی
        print(f"\n🎯 خلاصه ساختار نهایی:")
        print_structure(namaz_paltarinin_sertleri_item, 0)
        
        # بررسی اینکه آیا مسئله 57 یافت شد
        found_57 = False
        for child in namaz_paltarinin_sertleri_item['children']:
            if child['title'] == 'Namaz paltarının şərtləri' and child.get('masael_contents'):
                for masael in child['masael_contents']:
                    if 'Məsələ 57' in masael['header']:
                        found_57 = True
                        break
        
        if found_57:
            print(f"\n🎉 عالی! مسئله 57 با موفقیت یافت شد!")
        else:
            print(f"\n⚠️ مسئله 57 یافت نشد")
        
        # بررسی تعداد زیرمجموعه‌ها
        expected_subcategories = [
            "Namaz paltarının şərtləri",  # masael
            "1) Namaz paltarı pak olmalıdır",  # sub_category
            "2) Namaz paltarı mübah olmalıdır",  # sub_category
            "3) \"Murdar\"ın hissələrindən olmamalıdır",  # sub_category
            "4) Namaz paltarı əti haram heyvanın hissələrindən olmamalıdır",  # sub_category
            "5) Kişinin paltarı qızıldan olmamalıdır",  # sub_category
            "6) Kişinin paltarı xalis ipəkdən olmamalıdır",  # sub_category
            "Namaz paltarına aid müstəhəb və məkruh işlər"  # masael
        ]
        
        found_titles = [child['title'] for child in namaz_paltarinin_sertleri_item['children']]
        
        print(f"\n📋 بررسی زیرمجموعه‌های مورد انتظار:")
        for expected in expected_subcategories:
            if expected in found_titles:
                print(f"  ✅ {expected}")
            else:
                print(f"  ❌ {expected} (یافت نشد)")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return False


def print_structure(item, depth=0):
    """نمایش ساختار سلسله مراتبی"""
    indent = "  " * depth
    icon = "📂" if item['type'] == 'sub_category' else "📄"
    
    masael_count = len(item.get('masael_contents', []))
    masael_info = f" ({masael_count} مسئله)" if masael_count > 0 else ""
    
    print(f"{indent}{icon} {item['title']} ({item['type']}){masael_info}")
    
    if item.get('children'):
        for child in item['children'][:8]:  # فقط 8 مورد اول
            print_structure(child, depth + 1)


if __name__ == "__main__":
    success = test_namaz_paltarinin_sertleri()
    if success:
        print(f"\n🎯 تست موفق! ساختار 'Namaz paltarının şərtləri' درست کار می‌کند.")
    else:
        print(f"\n💥 تست ناموفق!")
