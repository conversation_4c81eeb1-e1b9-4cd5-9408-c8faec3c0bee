#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بررسی صفحه "Namaz vaxtlarının hökmləri" برای تشخیص نوع صحیح
"""

import requests
import re
from bs4 import BeautifulSoup


def analyze_namaz_vakti_page():
    """تحلیل صفحه Namaz vaxtlarının hökmləri"""
    print("🔍 تحلیل صفحه 'Namaz vaxtlarının hökmləri'")
    
    url = "https://www.leader.ir/az/book/246/1?sn=31663"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        print(f"✅ دریافت موفق: {response.status_code}")
        
        # جستجوی عناصر strong که شامل "Məsələ" هستند
        print("\n🔍 جستجوی مسائل:")
        strong_elements = soup.find_all('strong')
        masael_count = 0
        
        for strong in strong_elements:
            text = strong.get_text().strip()
            if re.match(r'Məsələ\s+\d+\.', text, re.IGNORECASE):
                masael_count += 1
                number_match = re.search(r'Məsələ\s+(\d+)\.', text)
                if number_match:
                    number = int(number_match.group(1))
                    print(f"  {masael_count}. {text} (شماره: {number})")
                    
                    # نمایش محتوای کوتاه
                    parent = strong.parent
                    if parent:
                        full_text = parent.get_text()
                        content = full_text.replace(text, '', 1).strip()
                        print(f"     محتوا: {content[:80]}...")
        
        print(f"\n📊 تعداد مسائل یافت شده: {masael_count}")
        
        # بررسی محدوده مسائل
        page_text = soup.get_text()
        
        # جستجوی مسائل 16 تا 26
        target_masael = []
        for i in range(16, 27):  # 16 تا 26
            pattern = rf'Məsələ\s+{i}\.'
            if re.search(pattern, page_text, re.IGNORECASE):
                target_masael.append(i)
        
        print(f"\n🎯 مسائل 16-26 یافت شده: {target_masael}")
        
        if target_masael:
            print("✅ این صفحه حاوی مسائل 16-26 است، پس باید masael باشد!")
        else:
            print("❌ مسائل 16-26 یافت نشد")
        
        # نمایش چند مسئله اول
        print(f"\n📝 چند مسئله اول:")
        masael_pattern = re.compile(
            r'(Məsələ\s+\d+\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)', 
            re.DOTALL | re.IGNORECASE
        )
        
        matches = masael_pattern.findall(page_text)
        for i, (header, text) in enumerate(matches[:5], 1):
            clean_text = text.strip()
            clean_text = re.sub(r'\s+', ' ', clean_text)
            print(f"  {i}. {header}")
            print(f"     {clean_text[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ خطا: {e}")
        return False


if __name__ == "__main__":
    analyze_namaz_vakti_page()
