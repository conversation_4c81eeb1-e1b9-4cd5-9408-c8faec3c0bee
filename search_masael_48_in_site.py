#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
جستجوی مسائل 48 در سایت leader.ir
"""

import requests
import re
from bs4 import BeautifulSoup


def search_masael_48_in_site():
    """جستجوی مسائل 48 در سایت"""
    print("🔍 جستجوی مسائل 48 در سایت leader.ir")
    print("=" * 50)
    
    # بررسی صفحات مختلف با sn های مختلف
    base_url = "https://www.leader.ir/az/book/246/1?sn="
    
    # محدوده sn ها برای بررسی (اطراف 31668)
    sn_range = range(31665, 31675)  # 31665 تا 31674
    
    for sn in sn_range:
        url = f"{base_url}{sn}"
        print(f"\n🔍 بررسی sn={sn}: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                page_text = soup.get_text()
                
                # جستجوی مسئله 48
                if "Məsələ 48" in page_text:
                    print(f"✅ مسئله 48 در sn={sn} یافت شد!")
                    
                    # استخراج عنوان صفحه
                    title_element = soup.find('title')
                    if title_element:
                        title = title_element.get_text().strip()
                        print(f"📄 عنوان صفحه: {title}")
                    
                    # استخراج محتوای مسئله 48
                    pattern_48 = r'(Məsələ\s+48\.)\s*(.*?)(?=Məsələ\s+\d+\.|$)'
                    match_48 = re.search(pattern_48, page_text, re.DOTALL | re.IGNORECASE)
                    if match_48:
                        header, text = match_48.groups()
                        clean_text = text.strip()
                        clean_text = re.sub(r'\s+', ' ', clean_text)
                        print(f"📝 {header}")
                        print(f"   {clean_text[:200]}...")
                    
                    # بررسی مسائل 48-56
                    found_numbers = []
                    for num in range(48, 57):
                        if f"Məsələ {num}" in page_text:
                            found_numbers.append(num)
                    
                    print(f"📋 مسائل 48-56 یافت شده: {found_numbers}")
                    
                    return sn, url
                else:
                    print(f"❌ مسئله 48 یافت نشد")
            else:
                print(f"❌ خطا {response.status_code}")
                
        except Exception as e:
            print(f"❌ خطا: {e}")
    
    print(f"\n💥 مسئله 48 در هیچ‌کدام از sn های بررسی شده یافت نشد!")
    return None, None


if __name__ == "__main__":
    sn, url = search_masael_48_in_site()
    if sn:
        print(f"\n🎯 URL صحیح مسائل 48-56: {url}")
    else:
        print(f"\n❓ مسائل 48-56 ممکن است در بخش دیگری از سایت باشند")
