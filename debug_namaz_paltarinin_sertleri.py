#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
دیباگ مشکل "Namaz paltarının şərtləri"
"""

from crawler import LeaderCrawler


def debug_namaz_paltarinin_sertleri():
    """دیباگ مشکل Namaz paltarının şərtləri"""
    print("🔍 دیباگ مشکل 'Namaz paltarının şərtləri'")
    print("=" * 60)
    
    crawler = LeaderCrawler()
    
    # شبیه‌سازی ساختار کامل "Namazda örtünmə"
    namazda_ortunme_item = {
        'id': '31668',
        'title': 'Namazda örtünmə',
        'type': 'sub_category',
        'url': 'https://www.leader.ir/az/book/246/1?sn=31668',
        'children': []
    }
    
    print(f"📂 آیتم اصلی: {namazda_ortunme_item['title']}")
    
    # مرحله 1: استخراج زیرمجموعه‌های "Namazda örtünmə"
    print(f"\n🔍 مرحله 1: استخراج زیرمجموعه‌های 'Namazda örtünmə'...")
    crawler.extract_deep_subcategories(namazda_ortunme_item)
    
    print(f"📊 تعداد زیرمجموعه‌ها: {len(namazda_ortunme_item['children'])}")
    
    # پیدا کردن "Namaz paltarının şərtləri"
    namaz_paltarinin_sertleri = None
    for child in namazda_ortunme_item['children']:
        if child['title'] == 'Namaz paltarının şərtləri':
            namaz_paltarinin_sertleri = child
            break
    
    if not namaz_paltarinin_sertleri:
        print(f"❌ 'Namaz paltarının şərtləri' یافت نشد!")
        return
    
    print(f"\n✅ 'Namaz paltarının şərtləri' یافت شد:")
    print(f"  ID: {namaz_paltarinin_sertleri['id']}")
    print(f"  نوع: {namaz_paltarinin_sertleri['type']}")
    print(f"  URL: {namaz_paltarinin_sertleri['url']}")
    print(f"  تعداد children: {len(namaz_paltarinin_sertleri.get('children', []))}")
    
    # مرحله 2: بررسی چرا children خالی است
    print(f"\n🔍 مرحله 2: بررسی چرا children خالی است...")
    
    # تست تشخیص دسته‌بندی خاص
    is_special = crawler._handle_special_subcategory(namaz_paltarinin_sertleri)
    print(f"  آیا دسته‌بندی خاص تشخیص داده شد؟ {is_special}")
    
    if is_special:
        print(f"  ✅ دسته‌بندی خاص تشخیص داده شد")
        print(f"  📊 تعداد children بعد از تشخیص: {len(namaz_paltarinin_sertleri.get('children', []))}")
        
        # نمایش children
        for i, child in enumerate(namaz_paltarinin_sertleri.get('children', []), 1):
            print(f"    {i}. {child['title']} (نوع: {child['type']}, ID: {child['id']})")
    else:
        print(f"  ❌ دسته‌بندی خاص تشخیص داده نشد")
        
        # تست روش عادی
        print(f"  🔄 تست روش عادی...")
        try:
            # فراخوانی مستقیم extract_deep_subcategories
            crawler.extract_deep_subcategories(namaz_paltarinin_sertleri, depth=1)
            print(f"  📊 تعداد children بعد از روش عادی: {len(namaz_paltarinin_sertleri.get('children', []))}")
        except Exception as e:
            print(f"  ❌ خطا در روش عادی: {e}")
    
    # مرحله 3: تست مستقیم
    print(f"\n🔍 مرحله 3: تست مستقیم...")
    
    # ایجاد آیتم جدید برای تست مستقیم
    test_item = {
        'id': '31669',
        'title': 'Namaz paltarının şərtləri',
        'type': 'sub_category',
        'url': 'https://www.leader.ir/az/book/246/NAMAZ-VƏ-ORUC-RİSALƏSİ?sn=31669',
        'children': []
    }
    
    print(f"  🧪 تست مستقیم با آیتم جدید...")
    crawler.extract_deep_subcategories(test_item, depth=0)
    
    print(f"  📊 تعداد children در تست مستقیم: {len(test_item.get('children', []))}")
    
    if test_item.get('children'):
        print(f"  ✅ تست مستقیم موفق!")
        for i, child in enumerate(test_item['children'], 1):
            print(f"    {i}. {child['title']} (نوع: {child['type']}, ID: {child['id']})")
    else:
        print(f"  ❌ تست مستقیم ناموفق!")
    
    # مرحله 4: بررسی processed_items
    print(f"\n🔍 مرحله 4: بررسی processed_items...")
    print(f"  📋 آیتم‌های پردازش شده: {len(crawler.processed_items)}")
    
    # بررسی اینکه آیا این آیتم قبلاً پردازش شده
    item_keys = [
        f"31669_0",
        f"31669_1", 
        f"31669_2"
    ]
    
    for key in item_keys:
        if key in crawler.processed_items:
            print(f"  ⚠️ کلید '{key}' قبلاً پردازش شده است")
        else:
            print(f"  ✅ کلید '{key}' پردازش نشده است")


if __name__ == "__main__":
    debug_namaz_paltarinin_sertleri()
